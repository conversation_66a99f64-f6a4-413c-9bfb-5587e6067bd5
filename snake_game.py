#!/usr/bin/env python3
"""
Simple Snake Game

A classic snake game implementation using Pygame.
"""
import pygame
import random
import sys
from typing import List, Tu<PERSON>, Optional
from enum import Enum, auto

# Initialize pygame
pygame.init()

# Define constants
class Direction(Enum):
    """Enum representing the possible directions of snake movement."""
    UP = auto()
    DOWN = auto()
    LEFT = auto()
    RIGHT = auto()

# Colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GREEN = (0, 255, 0)
RED = (255, 0, 0)

# Game settings
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
GRID_SIZE = 20
GRID_WIDTH = WINDOW_WIDTH // GRID_SIZE
GRID_HEIGHT = WINDOW_HEIGHT // GRID_SIZE
SNAKE_SPEED = 10  # Frames per second

class SnakeGame:
    """Main class that manages the snake game."""
    
    def __init__(self) -> None:
        """Initialize the game, snake, and food."""
        self.screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption('Snake Game')
        self.clock = pygame.time.Clock()
        self.font = pygame.font.SysFont('Arial', 25)
        
        self.reset_game()
    
    def reset_game(self) -> None:
        """Reset the game state."""
        # Initial snake position (middle of the screen)
        self.snake: List[Tuple[int, int]] = [
            (GRID_WIDTH // 2, GRID_HEIGHT // 2),
            (GRID_WIDTH // 2 - 1, GRID_HEIGHT // 2),
            (GRID_WIDTH // 2 - 2, GRID_HEIGHT // 2)
        ]
        
        self.direction = Direction.RIGHT
        self.score = 0
        self.food = self.generate_food()
        self.game_over = False
    
    def generate_food(self) -> Tuple[int, int]:
        """Generate food at a random position.
        
        Returns:
            Tuple[int, int]: The (x, y) coordinates of the food.
        """
        while True:
            food = (
                random.randint(0, GRID_WIDTH - 1),
                random.randint(0, GRID_HEIGHT - 1)
            )
            # Make sure food doesn't appear on snake
            if food not in self.snake:
                return food
    
    def handle_events(self) -> bool:
        """Handle keyboard events.
        
        Returns:
            bool: True if game should continue, False if it should quit.
        """
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
            elif event.type == pygame.KEYDOWN:
                if self.game_over and event.key == pygame.K_RETURN:
                    self.reset_game()
                    return True
                
                if event.key == pygame.K_UP and self.direction != Direction.DOWN:
                    self.direction = Direction.UP
                elif event.key == pygame.K_DOWN and self.direction != Direction.UP:
                    self.direction = Direction.DOWN
                elif event.key == pygame.K_LEFT and self.direction != Direction.RIGHT:
                    self.direction = Direction.LEFT
                elif event.key == pygame.K_RIGHT and self.direction != Direction.LEFT:
                    self.direction = Direction.RIGHT
                elif event.key == pygame.K_ESCAPE:
                    return False
        return True
    
    def update(self) -> None:
        """Update game state including snake movement and collision detection."""
        if self.game_over:
            return
        
        # Get the current head position
        head_x, head_y = self.snake[0]
        
        # Calculate new head position based on direction
        if self.direction == Direction.UP:
            head_y -= 1
        elif self.direction == Direction.DOWN:
            head_y += 1
        elif self.direction == Direction.LEFT:
            head_x -= 1
        elif self.direction == Direction.RIGHT:
            head_x += 1
        
        # The new head position
        new_head = (head_x, head_y)
        
        # Check for collisions with walls
        if (head_x < 0 or head_x >= GRID_WIDTH or 
            head_y < 0 or head_y >= GRID_HEIGHT):
            self.game_over = True
            return
        
        # Check for collision with self
        if new_head in self.snake[1:]:
            self.game_over = True
            return
        
        # Move the snake
        self.snake.insert(0, new_head)
        
        # Check if snake ate the food
        if new_head == self.food:
            self.score += 1
            self.food = self.generate_food()
        else:
            # Remove the tail
            self.snake.pop()
    
    def draw(self) -> None:
        """Draw the game elements on the screen."""
        self.screen.fill(BLACK)
        
        # Draw snake
        for segment in self.snake:
            pygame.draw.rect(
                self.screen, 
                GREEN, 
                (segment[0] * GRID_SIZE, segment[1] * GRID_SIZE, GRID_SIZE, GRID_SIZE)
            )
        
        # Draw food
        pygame.draw.rect(
            self.screen, 
            RED, 
            (self.food[0] * GRID_SIZE, self.food[1] * GRID_SIZE, GRID_SIZE, GRID_SIZE)
        )
        
        # Draw score
        score_text = self.font.render(f'Score: {self.score}', True, WHITE)
        self.screen.blit(score_text, (10, 10))
        
        # Draw game over message
        if self.game_over:
            game_over_text = self.font.render('Game Over! Press Enter to restart', True, WHITE)
            self.screen.blit(
                game_over_text, 
                (WINDOW_WIDTH // 2 - game_over_text.get_width() // 2, 
                 WINDOW_HEIGHT // 2 - game_over_text.get_height() // 2)
            )
        
        pygame.display.update()
    
    def run(self) -> None:
        """Main game loop."""
        running = True
        
        while running:
            running = self.handle_events()
            if not running:
                break
                
            self.update()
            self.draw()
            self.clock.tick(SNAKE_SPEED)

def main() -> None:
    """Main function to start the game."""
    game = SnakeGame()
    game.run()
    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    main()