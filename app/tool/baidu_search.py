import asyncio
from typing import List, Optional

from app.logger import logger
from app.tool.base import BaseTool
from app.tool.search import BaiduSearchEngine
from app.tool.web_search import SearchMetadata, SearchResponse, SearchResult


class BaiduSearch(BaseTool):
    """Tool for performing searches specifically using the Baidu search engine."""

    name: str = "baidu_search"
    description: str = """Search the web using Baidu search engine.
    This tool returns search results with relevant information, URLs, titles, and descriptions
    specifically from Baidu, which is particularly useful for Chinese language queries."""
    parameters: dict = {
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "(required) The search query to submit to Bai<PERSON>.",
            },
            "num_results": {
                "type": "integer",
                "description": "(optional) The number of search results to return. Default is 5.",
                "default": 5,
            },
            "lang": {
                "type": "string",
                "description": "(optional) Language code for search results (default: zh).",
                "default": "zh",
            },
            "country": {
                "type": "string",
                "description": "(optional) Country code for search results (default: cn).",
                "default": "cn",
            },
            "fetch_content": {
                "type": "boolean",
                "description": "(optional) Whether to fetch full content from result pages. Default is false.",
                "default": False,
            },
        },
        "required": ["query"],
    }
    _search_engine = BaiduSearchEngine()

    async def execute(
        self,
        query: str,
        num_results: int = 5,
        lang: Optional[str] = "zh",
        country: Optional[str] = "cn",
        fetch_content: bool = False,
    ) -> SearchResponse:
        """
        Execute a Baidu search and return detailed search results.

        Args:
            query: The search query to submit to Baidu
            num_results: The number of search results to return (default: 5)
            lang: Language code for search results (default: zh)
            country: Country code for search results (default: cn)
            fetch_content: Whether to fetch content from result pages (default: False)

        Returns:
            A structured response containing search results and metadata
        """
        logger.info(f"🔎 Performing Baidu search for: {query}")
        
        # Set search parameters
        search_params = {
            "lang": lang,
            "country": country,
        }
        
        try:
            # Perform the search
            search_items = await self._perform_search(
                query, num_results, search_params
            )
            
            if not search_items:
                return SearchResponse(
                    query=query,
                    error="Baidu search returned no results.",
                    results=[],
                )
            
            # Transform search items into structured results
            results = [
                SearchResult(
                    position=i + 1,
                    url=item.url,
                    title=item.title or f"Result {i+1}",
                    description=item.description or "",
                    source="baidu",
                )
                for i, item in enumerate(search_items)
            ]
            
            # Return a successful structured response
            return SearchResponse(
                status="success",
                query=query,
                results=results,
                metadata=SearchMetadata(
                    total_results=len(results),
                    language=lang,
                    country=country,
                ),
            )
            
        except Exception as e:
            logger.error(f"Error performing Baidu search: {str(e)}")
            return SearchResponse(
                query=query,
                error=f"Error performing Baidu search: {str(e)}",
                results=[],
            )

    async def _perform_search(self, query, num_results, search_params):
        """Execute search with the Baidu engine."""
        return await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: list(
                self._search_engine.perform_search(
                    query,
                    num_results=num_results,
                    lang=search_params.get("lang"),
                    country=search_params.get("country"),
                )
            ),
        )


if __name__ == "__main__":
    baidu_search = BaiduSearch()
    search_response = asyncio.run(
        baidu_search.execute(
            query="Python 编程", fetch_content=False, num_results=3
        )
    )
    print(search_response.to_tool_result())
