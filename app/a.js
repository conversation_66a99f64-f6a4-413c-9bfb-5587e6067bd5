`);else if(f.type==="text")o(f.text??"");else if(f.type==="image"){if(typeof((_=f.attrs)==null?void 0:_.src)!="string")return void console.error("Image source is not a string: ",(b=f.attrs)==null?void 0:b.src);if(f.attrs.isLoading)return;const q=(S=f.attrs)==null?void 0:S.title,D=this._fileNameToImageFormat(q);i.push({id:i.length,type:te.IMAGE_ID,image_id_node:{image_id:f.attrs.src,format:D}})}else if(f.type==="mention"){const q=(A=f.attrs)==null?void 0:A.data;q&&Aa(q)?i.push({id:i.length,type:te.TEXT,text_node:{content:Cf(this._chatFlagModel,q.personality.type)}}):o(`@${ q == null ? void 0 : q.name } `)}};return l(n),i});this._extensionClient=n,this._chatFlagModel=i,this._specialContextInputModel=o,this._saveConversation=l,this._state={...xt.create()},this._totalCharactersStore=this._createTotalCharactersStore()}_createTotalCharactersStore(){return wf(()=>{let n=0;const i=this._state.chatHistory;return this._convertHistoryToExchanges(i).forEach(o=>{n+=JSON.stringify(o).length}),this._state.draftExchange&&(n+=JSON.stringify(this._state.draftExchange).length),n},0,this._totalCharactersCacheThrottleMs)}async decidePersonaType(){var n;try{return(((n=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:n.reduce((o,l)=>o+l,0))||0)<=4?Pt.PROTOTYPER:Pt.DEFAULT}catch(i){return console.error("Error determining persona type:",i),Pt.DEFAULT}}static create(n={}){const i=new Date().toISOString();return{id:crypto.randomUUID(),name:void 0,createdAtIso:i,lastInteractedAtIso:i,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:Pt.DEFAULT,...n}}static toSentenceCase(n){return n.charAt(0).toUpperCase()+n.slice(1)}static getDisplayName(n){var l;const i=this._filterToExchanges(n);let o;return o=df(n)?"Autofix Chat":Kn(n)?"New Agent":"New Chat",xt.toSentenceCase(n.name||((l=i[0])==null?void 0:l.request_message)||o)}static _filterToExchanges(n){return n.chatHistory.filter(i=>de(i))}static isEmpty(n){var i;return n.chatHistory.filter(o=>de(o)).length===0&&!((i=n.draftExchange)!=null&&i.request_message)}static isNamed(n){return n.name!==void 0&&n.name!==""}static getTime(n,i){return i==="lastMessageTimestamp"?xt.lastMessageTimestamp(n):i==="lastInteractedAt"?xt.lastInteractedAt(n):xt.createdAt(n)}static createdAt(n){return new Date(n.createdAtIso)}static lastInteractedAt(n){return new Date(n.lastInteractedAtIso)}static lastMessageTimestamp(n){const i=this._filterToExchanges(n);if(i.length===0)return this.createdAt(n);const o=i[i.length-1];return o.timestamp?new Date(o.timestamp):this.createdAt(n)}static isValid(n){return n.id!==void 0&&(!xt.isEmpty(n)||xt.isNamed(n))}onBeforeChangeConversation(n){return this._onBeforeChangeConversationListeners.push(n),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(i=>i!==n)}}_notifyBeforeChangeConversation(n,i){let o=i;for(const l of this._onBeforeChangeConversationListeners){const f=l(n,o);f!==void 0&&(o=f)}return o}get extraData(){return this._state.extraData}set extraData(n){this.update({extraData:n})}get focusModel(){return this._focusModel}get isValid(){return xt.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??Pt.DEFAULT}get displayName(){return xt.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return xt.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}addChatItem(n){this.addExchange(n)}get requestIds(){return this._state.chatHistory.map(n=>n.request_id).filter(n=>n!==void 0)}get hasDraft(){var o;const n=(((o=this.draftExchange)==null?void 0:o.request_message)??"").trim()!=="",i=this.hasImagesInDraft();return n||i}hasImagesInDraft(){var o;const n=(o=this.draftExchange)==null?void 0:o.rich_text_json_repr;if(!n)return!1;const i=l=>Array.isArray(l)?l.some(i):!!l&&(l.type==="image"||!(!l.content||!Array.isArray(l.content))&&l.content.some(i));return i(n)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){const n=xt._filterToExchanges(this);return n.length===0?null:n[0]}get lastExchange(){const n=xt._filterToExchanges(this);return n.length===0?null:n[n.length-1]}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(n=>de(n)&&n.status===ut.sent)}get successfulMessages(){return this._state.chatHistory.filter(n=>Jn(n)||wr(n))}get totalCharactersStore(){return this._totalCharactersStore}_convertHistoryToExchanges(n){if(n.length===0)return[];const i=[];for(const o of n)if(Jn(o))i.push(Ef(o));else if(wr(o)&&o.fromTimestamp!==void 0&&o.toTimestamp!==void 0&&o.revertTarget){const l=If(o),f={request_message:"",response_text:"",request_id:o.request_id||crypto.randomUUID(),request_nodes:[l],response_nodes:[]};i.push(f)}return i}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===ut.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(n){const i=crypto.randomUUID();let o,l="";const f=await this._addIdeStateNode(bc({...n,request_id:i,status:ut.sent,timestamp:new Date().toISOString()}));for await(const _ of this.sendUserMessage(i,f))_.response_text&&(l+=_.response_text),_.request_id&&(o=_.request_id);return{responseText:l,requestId:o}}async*getChatStream(n){n.request_id&&(yield*this._extensionClient.getExistingChatStream(n,{flags:this._chatFlagModel}))}async*sendUserMessage(n,i){var A;const o=this._specialContextInputModel.chatActiveContext;let l=this.successfulMessages;if(i.chatItemType===Yn.summaryTitle){const q=l.findIndex(D=>D.chatItemType!==Yn.agentOnboarding&&ba(D));q!==-1&&(l=l.slice(q))}const f=i.disableHistory===!0?[]:l;let _=this._convertHistoryToExchanges(f);this._chatFlagModel.truncateChatHistory&&(_=this._truncatedChatHistory.getTruncatedChatHistory(_,()=>this._extensionClient.reportAgentSessionEvent({eventName:Fa.chatHistoryTruncated,conversationId:this.id})));let b=this.personaType;if(i.structured_request_nodes){const q=i.structured_request_nodes.find(D=>D.type===te.CHANGE_PERSONALITY);q&&q.change_personality_node&&(b=q.change_personality_node.personality_type)}const S={text:i.request_message,chatHistory:_,modelId:i.model_id,context:o,userSpecifiedFiles:o.userSpecifiedFiles,externalSourceIds:(A=o.externalSources)==null?void 0:A.map(q=>q.id),disableRetrieval:i.disableRetrieval??!1,disableSelectedCodeDetails:i.disableSelectedCodeDetails??!1,nodes:i.structured_request_nodes,memoriesInfo:i.memoriesInfo,personaType:b};yield*this._extensionClient.startChatStreamWithRetry(n,S,{flags:this._chatFlagModel})}onSendExchange(n){return this._onSendExchangeListeners.push(n),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(i=>i!==n)}}onNewConversation(n){return this._onNewConversationListeners.push(n),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(i=>i!==n)}}onHistoryDelete(n){return this._onHistoryDeleteListeners.push(n),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(i=>i!==n)}}updateChatItem(n,i){return this.chatHistory.find(o=>o.request_id===n)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(o=>o.request_id===n?{...o,...i}:o)}),!0)}_fileNameToImageFormat(n){var o;switch((o=n.split(".").at(-1))==null?void 0:o.toLowerCase()){case"jpeg":case"jpg":return Zn.JPEG;case"png":return Zn.PNG;case"gif":return Zn.GIF;case"webp":return Zn.WEBP;default:return Zn.IMAGE_FORMAT_UNSPECIFIED}}async _addIdeStateNode(n){let i=(n.structured_request_nodes??[]).filter(l=>l.type!==te.IDE_STATE);const o=await this._extensionClient.getChatRequestIdeState();return o?(i=[...i,{id:Wc(i)+1,type:te.IDE_STATE,ide_state_node:o}],{...n,structured_request_nodes:i}):n}}function If(s){const n=(wr(s),s.fromTimestamp),i=(wr(s),s.toTimestamp),o=wr(s)&&s.revertTarget!==void 0;return{id:Date.now(),type:te.CHECKPOINT_REF,checkpoint_ref_node:{request_id:s.request_id||"",from_timestamp:n,to_timestamp:i,source:o?kc.CHECKPOINT_REVERT:void 0}}}function Ef(s){const n=(s.structured_output_nodes??[]).filter(i=>i.type===ee.RAW_RESPONSE||i.type===ee.TOOL_USE);return{request_message:s.request_message,response_text:s.response_text??"",request_id:s.request_id||"",request_nodes:s.structured_request_nodes??[],response_nodes:n}}function Wc(s){return s.length>0?Math.max(...s.map(n=>n.id)):0}function bc(s){var n;if(s.request_message.length>0&&!((n=s.structured_request_nodes)!=null&&n.some(i=>i.type===te.TEXT))){let i=s.structured_request_nodes??[];return i=[...i,{id:Wc(i)+1,type:te.TEXT,text_node:{content:s.request_message}}],{...s,structured_request_nodes:i}}return s}class Tf{constructor(n,i,o){d(this,"_cachedTruncatedHistory",[]);d(this,"_cachedTruncatedChars",[]);d(this,"_cachedUntruncatedChars",new Map);this._maxHistoryChars=n,this._truncateToChars=i,this._avoidTruncationExchangeCount=o}getTruncatedChatHistory(n,i){const o=[];n.length<this._cachedTruncatedHistory.length&&(this._cachedTruncatedHistory.splice(n.length),this._cachedTruncatedChars.splice(n.length));for(let S=0;S<this._cachedTruncatedHistory.length;S++){const A=n[S],q=this._cachedTruncatedHistory[S];if(A.request_id!==q.request_id){this._cachedTruncatedHistory.splice(S),this._cachedTruncatedChars.splice(S);break}}const l=this._cachedTruncatedHistory.length;o.push(...this._cachedTruncatedHistory);let f=this._cachedTruncatedChars.reduce((S,A)=>S+A,0);const _=n.slice(l).map(S=>this._getUntruncatedChars(S)).reduce((S,A)=>S+A,0);if(f+_<this._maxHistoryChars)return o.push(...n.slice(l)),o;i==null||i();let b=f+_-this._truncateToChars;for(let S=l;S<n.length-this._avoidTruncationExchangeCount;S++){const A=this._truncateExchange(n[S]),q=JSON.stringify(A).length;if(this._cachedTruncatedHistory.push(A),this._cachedTruncatedChars.push(q),o.push(A),f+=q,b-=this._getUntruncatedChars(n[S])-q,b<=0)break}return o.push(...n.slice(o.length)),o}_truncateExchange(n){var f,_;const i=(f=n.request_nodes)==null?void 0:f.map(b=>b.type===te.TOOL_RESULT?{...b,tool_result_node:{...b.tool_result_node,content:"[Tool result truncated...]"}}:b),o=(_=n.response_nodes)==null?void 0:_.map(b=>b.type===ee.TOOL_USE?{...b,tool_use:b.tool_use&&this._truncateToolUse(b.tool_use)}:b),l={...n,request_nodes:i,response_nodes:o};return JSON.stringify(l).length>JSON.stringify(n).length?n:l}_truncateToolUse(n){let i;try{i=JSON.parse(n.input_json)}catch{return{...n,input_json:JSON.stringify({message:"[Tool use truncated...]"})}}for(const o of Object.keys(i))i[o].length>14&&(i[o]="[Truncated...]");return{...n,input_json:JSON.stringify(i)}}_getUntruncatedChars(n){if(this._cachedUntruncatedChars.has(n.request_id))return this._cachedUntruncatedChars.get(n.request_id);{const i=JSON.stringify(n).length;return this._cachedUntruncatedChars.set(n.request_id,i),i}}}class Mf{constructor(n=!0,i=setTimeout){d(this,"_notify",new Set);d(this,"_clearTimeout",n=>{n.timeoutId&&clearTimeout(n.timeoutId)});d(this,"_schedule",n=>{if(!this._started||n.date&&(n.timeout=n.date.getTime()-Date.now(),n.timeout<0))return;const i=this._setTimeout;n.timeoutId=i(this._handle,n.timeout,n)});d(this,"_handle",n=>{n.notify(),n.date?this._notify.delete(n):n.once||this._schedule(n)});d(this,"dispose",()=>{this._notify.forEach(this._clearTimeout),this._notify.clear()});this._started=n,this._setTimeout=i}start(){return this._started||(this._started=!0,this._notify.forEach(this._schedule)),this}stop(){return this._started=!1,this._notify.forEach(this._clearTimeout),this}get isStarted(){return this._started}set isStarted(n){n?this.start():this.stop()}once(n,i){return this._register(n,i,!0)}interval(n,i){return this._register(n,i,!1)}at(n,i){return this._register(0,i,!1,typeof n=="number"?new Date(Date.now()+n):n)}reschedule(){this._notify.forEach(n=>{this._clearTimeout(n),this._schedule(n)})}_register(n,i,o,l){if(!n&&!l)return()=>{};const f={timeout:n,notify:i,once:o,date:l};return this._notify.add(f),this._schedule(f),()=>{this._clearTimeout(f),this._notify.delete(f)}}}class Af{constructor(n=0,i=0,o=new Mf,l=Rn("busy"),f=Rn(!1)){d(this,"unsubNotify");d(this,"unsubMessage");d(this,"activity",()=>{this.idleStatus.set("busy"),this.idleScheduler.reschedule()});d(this,"focus",n=>{this.focusAfterIdle.set(n)});this._idleNotifyTimeout=n,this._idleMessageTimeout=i,this.idleScheduler=o,this.idleStatus=l,this.focusAfterIdle=f,this.idleNotifyTimeout=n,this.idleMessageTimeout=i}set idleMessageTimeout(n){var i;this._idleMessageTimeout!==n&&(this._idleMessageTimeout=n,(i=this.unsubMessage)==null||i.call(this),this.unsubMessage=this.idleScheduler.once(n,()=>{this.idleStatus.set("idle-message")}))}set idleNotifyTimeout(n){var i;this._idleNotifyTimeout!==n&&(this._idleNotifyTimeout=n,(i=this.unsubNotify)==null||i.call(this),this.unsubNotify=this.idleScheduler.once(n,()=>{this.idleStatus.set("idle-notify")}))}get idleMessageTimeout(){return this._idleMessageTimeout}get idleNotifyTimeout(){return this._idleNotifyTimeout}get notifyEnabled(){return this._idleNotifyTimeout>0}get messageEnabled(){return this._idleMessageTimeout>0}dispose(){var n,i;(n=this.unsubNotify)==null||n.call(this),(i=this.unsubMessage)==null||i.call(this),this.idleScheduler.dispose(),this.idleStatus.set("busy"),this.focusAfterIdle.set(!1)}}const Ts=Rn("idle");var Ff=(s=>(s.manual="manual",s.auto="auto",s))(Ff||{});class Sg{constructor(n,i,o,l={}){d(this,"_state",{currentConversationId:void 0,conversations:{},agentExecutionMode:"manual",isAgentEditsCollapsed:!0});d(this,"extensionClient");d(this,"_chatFlagsModel");d(this,"_currConversationModel");d(this,"subscribers",new Set);d(this,"idleMessageModel",new Af);d(this,"isAgentEditsCollapsed");d(this,"agentExecutionMode");d(this,"sortConversationsBy");d(this,"onLoaded",async()=>{var i,o;const n=await this.extensionClient.getChatInitData();this._chatFlagsModel.update({enableEditableHistory:n.enableEditableHistory??!1,enablePreferenceCollection:n.enablePreferenceCollection??!1,enableRetrievalDataCollection:n.enableRetrievalDataCollection??!1,enableDebugFeatures:n.enableDebugFeatures??!1,enableRichTextHistory:n.useRichTextHistory??!0,modelDisplayNameToId:n.modelDisplayNameToId??{},fullFeatured:n.fullFeatured??!0,smallSyncThreshold:n.smallSyncThreshold??15,bigSyncThreshold:n.bigSyncThreshold??1e3,enableExternalSourcesInChat:n.enableExternalSourcesInChat??!1,enableSmartPaste:n.enableSmartPaste??!1,enableDirectApply:n.enableDirectApply??!1,summaryTitles:n.summaryTitles??!1,suggestedEditsAvailable:n.suggestedEditsAvailable??!1,enableShareService:n.enableShareService??!1,maxTrackableFileCount:n.maxTrackableFileCount??Lc,enableDesignSystemRichTextEditor:n.enableDesignSystemRichTextEditor??!1,enableSources:n.enableSources??!1,enableChatMermaidDiagrams:n.enableChatMermaidDiagrams??!1,smartPastePrecomputeMode:n.smartPastePrecomputeMode??Ac.visibleHover,useNewThreadsMenu:n.useNewThreadsMenu??!1,enableChatMermaidDiagramsMinVersion:n.enableChatMermaidDiagramsMinVersion??!1,idleNewSessionMessageTimeoutMs:n.idleNewSessionMessageTimeoutMs,idleNewSessionNotificationTimeoutMs:n.idleNewSessionNotificationTimeoutMs,enableChatMultimodal:n.enableChatMultimodal??!1,enableAgentMode:n.enableAgentMode??!1,agentMemoriesFilePathName:n.agentMemoriesFilePathName,enableRichCheckpointInfo:n.enableRichCheckpointInfo??!1,userTier:n.userTier??"unknown",truncateChatHistory:n.truncateChatHistory??!1,enableBackgroundAgents:n.enableBackgroundAgents??!1,enableVirtualizedMessageList:n.enableVirtualizedMessageList??!1,customPersonalityPrompts:n.customPersonalityPrompts??{},enablePersonalities:n.enablePersonalities??!1,memoryClassificationOnFirstToken:n.memoryClassificationOnFirstToken??!1}),(o=(i=this.options).onLoaded)==null||o.call(i),this.notifySubscribers()});d(this,"subscribe",n=>(this.subscribers.add(n),n(this),()=>{this.subscribers.delete(n)}));d(this,"initialize",n=>{this._state={...this._state,...this._host.getState()},n&&(this._state.conversations[n==null?void 0:n.id]=n),this._chatFlagsModel.fullFeatured&&((n==null?void 0:n.id)!==ha&&this.currentConversationId!==ha||(delete this._state.conversations[ha],this.setCurrentConversationToWelcome())),this._chatFlagsModel.subscribe(i=>{this.idleMessageModel.idleNotifyTimeout=i.idleNewSessionNotificationTimeoutMs,this.idleMessageModel.idleMessageTimeout=i.idleNewSessionMessageTimeoutMs}),this._state.conversations=Object.fromEntries(Object.entries(this._state.conversations).filter(([i,o])=>xt.isValid(o))),this.initializeIsShareableState(),n?this.setCurrentConversation(n.id):this.setCurrentConversation(this.currentConversationId),this.subscribe(()=>this.idleMessageModel.activity()),this.setState(this._state)});d(this,"initializeIsShareableState",()=>{const n={...this._state.conversations};for(const[i,o]of Object.entries(n)){if(o.isShareable)continue;const l=o.chatHistory.some(f=>Jn(f));n[i]={...o,isShareable:l}}this._state.conversations=n});d(this,"updateChatState",n=>{this._state={...this._state,...n};const i=this._state.conversations,o=new Set;for(const[l,f]of Object.entries(i))f.isPinned&&o.add(l);this.setState(this._state),this.notifySubscribers()});d(this,"saveImmediate",()=>{this._host.setState(this._state)});d(this,"setState",Yd(n=>{this._host.setState({...n,isAgentEditsCollapsed:xr(this.isAgentEditsCollapsed),agentExecutionMode:xr(this.agentExecutionMode),sortConversationsBy:xr(this.sortConversationsBy)})},1e3,{maxWait:15e3}));d(this,"notifySubscribers",()=>{this.subscribers.forEach(n=>n(this))});d(this,"withWebviewClientEvent",(n,i)=>(...o)=>(this.extensionClient.reportWebviewClientEvent(n),i(...o)));d(this,"setCurrentConversationToWelcome",()=>{this.setCurrentConversation(),this._currConversationModel.setName("Welcome to Augment"),this._currConversationModel.addChatItem({chatItemType:Yn.educateFeatures,request_id:crypto.randomUUID(),seen_state:Wt.seen})});d(this,"popCurrentConversation",async()=>{var i,o;const n=this.currentConversationId;n&&await this.deleteConversation(n,((i=this.nextConversation)==null?void 0:i.id)??((o=this.previousConversation)==null?void 0:o.id))});d(this,"setCurrentConversation",async(n,i=!0)=>{let o;n===void 0?(this.deleteInvalidConversations(Kn(this._currConversationModel)?"agent":"chat"),o=xt.create({personaType:await this._currConversationModel.decidePersonaType()})):o=this._state.conversations[n]??xt.create({personaType:await this._currConversationModel.decidePersonaType()});const l=this.conversations[this._currConversationModel.id]===void 0;this._currConversationModel.setConversation(o,!l,i),this._currConversationModel.recoverAllExchanges(),this._currConversationModel.resetTotalCharactersCache()});d(this,"saveConversation",n=>{this.updateChatState({conversations:{...this._state.conversations,[n.id]:n},currentConversationId:n.id})});d(this,"isConversationShareable",n=>{var i;return((i=this._state.conversations[n])==null?void 0:i.isShareable)??!0});d(this,"setSortConversationsBy",n=>{this.sortConversationsBy.set(n),this.updateChatState({})});d(this,"getConversationUrl",async n=>{const i=this._state.conversations[n];if(i.lastUrl)return i.lastUrl;Ts.set("copying");const o=i==null?void 0:i.chatHistory,l=o.reduce((b,S)=>(Jn(S)&&b.push({request_id:S.request_id||"",request_message:S.request_message,response_text:S.response_text||""}),b),[]);if(l.length===0)throw new Error("No chat history to share");const f=xt.getDisplayName(i),_=await this.extensionClient.saveChat(n,l,f);if(_.data){let b=_.data.url;return this.updateChatState({conversations:{...this._state.conversations,[n]:{...i,lastUrl:b}}}),b}throw new Error("Failed to create URL")});d(this,"shareConversation",async n=>{if(n!==void 0)try{const i=await this.getConversationUrl(n);if(!i)return void Ts.set("idle");navigator.clipboard.writeText(i),Ts.set("copied")}catch{Ts.set("failed")}});d(this,"deleteConversations",async(n,i=void 0)=>{if(await this.extensionClient.openConfirmationModal({title:"Delete Conversation",message:`Are you sure you want to delete ${ n.length > 1 ? "these conversations" : "this conversation" }?`,confirmButtonText:"Delete",cancelButtonText:"Cancel"})){const o=new Set(n);this.deleteConversationIds(o),this.currentConversationId&&o.has(this.currentConversationId)&&this.setCurrentConversation(i)}});d(this,"deleteConversation",async(n,i=void 0)=>{await this.deleteConversations([n],i)});d(this,"deleteConversationIds",async n=>{var o;const i=[];for(const l of n){const f=((o=this._state.conversations[l])==null?void 0:o.requestIds)??[];i.push(...f)}for(const l of Object.values(this._state.conversations))if(n.has(l.id)){for(const _ of l.chatHistory)de(_)&&this.deleteImagesInExchange(_);const f=l.draftExchange;f&&this.deleteImagesInExchange(f)}this.updateChatState({conversations:Object.fromEntries(Object.entries(this._state.conversations).filter(([l])=>!n.has(l)))}),this.extensionClient.clearMetadataFor({requestIds:i,conversationIds:Array.from(n)})});d(this,"deleteImagesInExchange",n=>{const i=new Set([...n.rich_text_json_repr?this.findImagesInJson(n.rich_text_json_repr):[],...n.structured_request_nodes?this.findImagesInStructuredRequest(n.structured_request_nodes):[]]);for(const o of i)this.deleteImage(o)});d(this,"findImagesInJson",n=>{const i=[],o=l=>{var f;if(l.type==="image"&&((f=l.attrs)!=null&&f.src))i.push(l.attrs.src);else if((l.type==="doc"||l.type==="paragraph")&&l.content)for(const _ of l.content)o(_)};return o(n),i});d(this,"findImagesInStructuredRequest",n=>n.reduce((i,o)=>(o.type===te.IMAGE_ID&&o.image_id_node&&i.push(o.image_id_node.image_id),i),[]));d(this,"toggleConversationPinned",n=>{const i=this._state.conversations[n],o={...i,isPinned:!i.isPinned};this.updateChatState({conversations:{...this._state.conversations,[n]:o}}),n===this.currentConversationId&&this._currConversationModel.toggleIsPinned()});d(this,"renameConversation",(n,i)=>{const o={...this._state.conversations[n],name:i};this.updateChatState({conversations:{...this._state.conversations,[n]:o}}),n===this.currentConversationId&&this._currConversationModel.setName(i)});d(this,"smartPaste",(n,i,o,l)=>{const f=this._currConversationModel.historyTo(n,!0).filter(_=>Jn(_)).map(_=>({request_message:_.request_message,response_text:_.response_text||"",request_id:_.request_id||""}));this.extensionClient.smartPaste({generatedCode:i,chatHistory:f,targetFile:o??void 0,options:l})});d(this,"saveImage",async n=>await this.extensionClient.saveImage(n));d(this,"deleteImage",async n=>await this.extensionClient.deleteImage(n));d(this,"renderImage",async n=>await this.extensionClient.loadImage(n));this._asyncMsgSender=n,this._host=i,this._specialContextInputModel=o,this.options=l,this._chatFlagsModel=new gf(l.initialFlags),this.extensionClient=new lf(this._host,this._asyncMsgSender,this._chatFlagsModel),this._currConversationModel=new xt(this.extensionClient,this._chatFlagsModel,this._specialContextInputModel,this.saveConversation),this.initialize(l.initialConversation),this.isAgentEditsCollapsed=Rn(this._state.isAgentEditsCollapsed),this.agentExecutionMode=Rn(this._state.agentExecutionMode??"manual"),this.sortConversationsBy=Rn(this._state.sortConversationsBy??"lastMessageTimestamp"),this.onLoaded()}get flags(){return this._chatFlagsModel}get specialContextInputModel(){return this._specialContextInputModel}get currentConversationId(){return this._state.currentConversationId}get currentConversationModel(){return this._currConversationModel}get conversations(){return this._state.conversations}orderedConversations(n,i="desc",o){const l=n||this._state.sortConversationsBy||"lastMessageTimestamp";let f=Object.values(this._state.conversations);return o&&(f=f.filter(o)),f.sort((_,b)=>{const S=xt.getTime(_,l).getTime(),A=xt.getTime(b,l).getTime();return i==="asc"?S-A:A-S})}get nextConversation(){if(!this.currentConversationId)return;const n=this.orderedConversations(),i=n.findIndex(o=>o.id===this.currentConversationId);return n.length>i+1?n[i+1]:void 0}get previousConversation(){if(!this.currentConversationId)return;const n=this.orderedConversations(),i=n.findIndex(o=>o.id===this.currentConversationId);return i>0?n[i-1]:void 0}get host(){return this._host}deleteInvalidConversations(n="all"){const i=Object.keys(this.conversations).filter(o=>{const l=!xt.isValid(this.conversations[o]),f=Kn(this.conversations[o]);return l&&(n==="agent"&&f||n==="chat"&&!f||n==="all")});i.length&&this.deleteConversationIds(new Set(i))}get lastMessageTimestamp(){const n=this.currentConversationModel.lastExchange;return n==null?void 0:n.timestamp}handleMessageFromExtension(n){return n.data.type===U.newThread&&(this.setCurrentConversation(),!0)}}const Vn=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,Sc=new Set,Sa=typeof process=="object"&&process?process:{},Bc=(s,n,i,o)=>{typeof Sa.emitWarning=="function"?Sa.emitWarning(s,n,i,o):console.error(`[${ i }] ${ n }: ${ s } `)};let ks=globalThis.AbortController,Cc=globalThis.AbortSignal;var Ec;if(ks===void 0){Cc=class{constructor(){d(this,"onabort");d(this,"_onabort",[]);d(this,"reason");d(this,"aborted",!1)}addEventListener(i,o){this._onabort.push(o)}},ks=class{constructor(){d(this,"signal",new Cc);n()}abort(i){var o,l;if(!this.signal.aborted){this.signal.reason=i,this.signal.aborted=!0;for(const f of this.signal._onabort)f(i);(l=(o=this.signal).onabort)==null||l.call(o,i)}}};let s=((Ec=Sa.env)==null?void 0:Ec.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const n=()=>{s&&(s=!1,Bc("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node - abort - controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",n))}}const dn=s=>s&&s===Math.floor(s)&&s>0&&isFinite(s),$c=s=>dn(s)?s<=Math.pow(2,8)?Uint8Array:s<=Math.pow(2,16)?Uint16Array:s<=Math.pow(2,32)?Uint32Array:s<=Number.MAX_SAFE_INTEGER?Rs:null:null;class Rs extends Array{constructor(n){super(n),this.fill(0)}}var Xn;const Mn=class Mn{constructor(n,i){d(this,"heap");d(this,"length");if(!g(Mn,Xn))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new i(n),this.length=0}static create(n){const i=$c(n);if(!i)return[];W(Mn,Xn,!0);const o=new Mn(n,i);return W(Mn,Xn,!1),o}push(n){this.heap[this.length++]=n}pop(){return this.heap[--this.length]}};Xn=new WeakMap,it(Mn,Xn,!1);let Ca=Mn;var Tc,Mc,Te,ue,Me,Ae,Qn,tr,Ft,Fe,Et,mt,J,Zt,ce,Gt,Ot,Re,Dt,qe,Oe,le,De,_n,Yt,F,wa,An,Xe,Ir,he,Vc,Fn,er,Er,fn,gn,Ia,qs,Os,_t,Ea,Cr,pn,Ta;const qa=class qa{constructor(n){it(this,F);it(this,Te);it(this,ue);it(this,Me);it(this,Ae);it(this,Qn);it(this,tr);d(this,"ttl");d(this,"ttlResolution");d(this,"ttlAutopurge");d(this,"updateAgeOnGet");d(this,"updateAgeOnHas");d(this,"allowStale");d(this,"noDisposeOnSet");d(this,"noUpdateTTL");d(this,"maxEntrySize");d(this,"sizeCalculation");d(this,"noDeleteOnFetchRejection");d(this,"noDeleteOnStaleGet");d(this,"allowStaleOnFetchAbort");d(this,"allowStaleOnFetchRejection");d(this,"ignoreFetchAbort");it(this,Ft);it(this,Fe);it(this,Et);it(this,mt);it(this,J);it(this,Zt);it(this,ce);it(this,Gt);it(this,Ot);it(this,Re);it(this,Dt);it(this,qe);it(this,Oe);it(this,le);it(this,De);it(this,_n);it(this,Yt);it(this,An,()=>{});it(this,Xe,()=>{});it(this,Ir,()=>{});it(this,he,()=>!1);it(this,Fn,n=>{});it(this,er,(n,i,o)=>{});it(this,Er,(n,i,o,l)=>{if(o||l)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});d(this,Tc,"LRUCache");const{max:i=0,ttl:o,ttlResolution:l=1,ttlAutopurge:f,updateAgeOnGet:_,updateAgeOnHas:b,allowStale:S,dispose:A,disposeAfter:q,noDisposeOnSet:D,noUpdateTTL:tt,maxSize:et=0,maxEntrySize:N=0,sizeCalculation:rt,fetchMethod:vt,memoMethod:z,noDeleteOnFetchRejection:dt,noDeleteOnStaleGet:ze,allowStaleOnFetchRejection:ne,allowStaleOnFetchAbort:bt,ignoreFetchAbort:Ce}=n;if(i!==0&&!dn(i))throw new TypeError("max option must be a nonnegative integer");const Rt=i?$c(i):Array;if(!Rt)throw new Error("invalid max value: "+i);if(W(this,Te,i),W(this,ue,et),this.maxEntrySize=N||g(this,ue),this.sizeCalculation=rt,this.sizeCalculation){if(!g(this,ue)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(z!==void 0&&typeof z!="function")throw new TypeError("memoMethod must be a function if defined");if(W(this,tr,z),vt!==void 0&&typeof vt!="function")throw new TypeError("fetchMethod must be a function if specified");if(W(this,Qn,vt),W(this,_n,!!vt),W(this,Et,new Map),W(this,mt,new Array(i).fill(void 0)),W(this,J,new Array(i).fill(void 0)),W(this,Zt,new Rt(i)),W(this,ce,new Rt(i)),W(this,Gt,0),W(this,Ot,0),W(this,Re,Ca.create(i)),W(this,Ft,0),W(this,Fe,0),typeof A=="function"&&W(this,Me,A),typeof q=="function"?(W(this,Ae,q),W(this,Dt,[])):(W(this,Ae,void 0),W(this,Dt,void 0)),W(this,De,!!g(this,Me)),W(this,Yt,!!g(this,Ae)),this.noDisposeOnSet=!!D,this.noUpdateTTL=!!tt,this.noDeleteOnFetchRejection=!!dt,this.allowStaleOnFetchRejection=!!ne,this.allowStaleOnFetchAbort=!!bt,this.ignoreFetchAbort=!!Ce,this.maxEntrySize!==0){if(g(this,ue)!==0&&!dn(g(this,ue)))throw new TypeError("maxSize must be a positive integer if specified");if(!dn(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");k(this,F,Vc).call(this)}if(this.allowStale=!!S,this.noDeleteOnStaleGet=!!ze,this.updateAgeOnGet=!!_,this.updateAgeOnHas=!!b,this.ttlResolution=dn(l)||l===0?l:1,this.ttlAutopurge=!!f,this.ttl=o||0,this.ttl){if(!dn(this.ttl))throw new TypeError("ttl must be a positive integer if specified");k(this,F,wa).call(this)}if(g(this,Te)===0&&this.ttl===0&&g(this,ue)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!g(this,Te)&&!g(this,ue)){const Ge="LRU_CACHE_UNBOUNDED";(ke=>!Sc.has(ke))(Ge)&&(Sc.add(Ge),Bc("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",Ge,qa))}}static unsafeExposeInternals(n){return{starts:g(n,Oe),ttls:g(n,le),sizes:g(n,qe),keyMap:g(n,Et),keyList:g(n,mt),valList:g(n,J),next:g(n,Zt),prev:g(n,ce),get head(){return g(n,Gt)},get tail(){return g(n,Ot)},free:g(n,Re),isBackgroundFetch:i=>{var o;return k(o=n,F,_t).call(o,i)},backgroundFetch:(i,o,l,f)=>{var _;return k(_=n,F,Os).call(_,i,o,l,f)},moveToTail:i=>{var o;return k(o=n,F,Cr).call(o,i)},indexes:i=>{var o;return k(o=n,F,fn).call(o,i)},rindexes:i=>{var o;return k(o=n,F,gn).call(o,i)},isStale:i=>{var o;return g(o=n,he).call(o,i)}}}get max(){return g(this,Te)}get maxSize(){return g(this,ue)}get calculatedSize(){return g(this,Fe)}get size(){return g(this,Ft)}get fetchMethod(){return g(this,Qn)}get memoMethod(){return g(this,tr)}get dispose(){return g(this,Me)}get disposeAfter(){return g(this,Ae)}getRemainingTTL(n){return g(this,Et).has(n)?1/0:0}*entries(){for(const n of k(this,F,fn).call(this))g(this,J)[n]===void 0||g(this,mt)[n]===void 0||k(this,F,_t).call(this,g(this,J)[n])||(yield[g(this,mt)[n],g(this,J)[n]])}*rentries(){for(const n of k(this,F,gn).call(this))g(this,J)[n]===void 0||g(this,mt)[n]===void 0||k(this,F,_t).call(this,g(this,J)[n])||(yield[g(this,mt)[n],g(this,J)[n]])}*keys(){for(const n of k(this,F,fn).call(this)){const i=g(this,mt)[n];i===void 0||k(this,F,_t).call(this,g(this,J)[n])||(yield i)}}*rkeys(){for(const n of k(this,F,gn).call(this)){const i=g(this,mt)[n];i===void 0||k(this,F,_t).call(this,g(this,J)[n])||(yield i)}}*values(){for(const n of k(this,F,fn).call(this))g(this,J)[n]===void 0||k(this,F,_t).call(this,g(this,J)[n])||(yield g(this,J)[n])}*rvalues(){for(const n of k(this,F,gn).call(this))g(this,J)[n]===void 0||k(this,F,_t).call(this,g(this,J)[n])||(yield g(this,J)[n])}[(Mc=Symbol.iterator,Tc=Symbol.toStringTag,Mc)](){return this.entries()}find(n,i={}){for(const o of k(this,F,fn).call(this)){const l=g(this,J)[o],f=k(this,F,_t).call(this,l)?l.__staleWhileFetching:l;if(f!==void 0&&n(f,g(this,mt)[o],this))return this.get(g(this,mt)[o],i)}}forEach(n,i=this){for(const o of k(this,F,fn).call(this)){const l=g(this,J)[o],f=k(this,F,_t).call(this,l)?l.__staleWhileFetching:l;f!==void 0&&n.call(i,f,g(this,mt)[o],this)}}rforEach(n,i=this){for(const o of k(this,F,gn).call(this)){const l=g(this,J)[o],f=k(this,F,_t).call(this,l)?l.__staleWhileFetching:l;f!==void 0&&n.call(i,f,g(this,mt)[o],this)}}purgeStale(){let n=!1;for(const i of k(this,F,gn).call(this,{allowStale:!0}))g(this,he).call(this,i)&&(k(this,F,pn).call(this,g(this,mt)[i],"expire"),n=!0);return n}info(n){const i=g(this,Et).get(n);if(i===void 0)return;const o=g(this,J)[i],l=k(this,F,_t).call(this,o)?o.__staleWhileFetching:o;if(l===void 0)return;const f={value:l};if(g(this,le)&&g(this,Oe)){const _=g(this,le)[i],b=g(this,Oe)[i];if(_&&b){const S=_-(Vn.now()-b);f.ttl=S,f.start=Date.now()}}return g(this,qe)&&(f.size=g(this,qe)[i]),f}dump(){const n=[];for(const i of k(this,F,fn).call(this,{allowStale:!0})){const o=g(this,mt)[i],l=g(this,J)[i],f=k(this,F,_t).call(this,l)?l.__staleWhileFetching:l;if(f===void 0||o===void 0)continue;const _={value:f};if(g(this,le)&&g(this,Oe)){_.ttl=g(this,le)[i];const b=Vn.now()-g(this,Oe)[i];_.start=Math.floor(Date.now()-b)}g(this,qe)&&(_.size=g(this,qe)[i]),n.unshift([o,_])}return n}load(n){this.clear();for(const[i,o]of n){if(o.start){const l=Date.now()-o.start;o.start=Vn.now()-l}this.set(i,o.value,o)}}set(n,i,o={}){var tt,et,N,rt,vt;if(i===void 0)return this.delete(n),this;const{ttl:l=this.ttl,start:f,noDisposeOnSet:_=this.noDisposeOnSet,sizeCalculation:b=this.sizeCalculation,status:S}=o;let{noUpdateTTL:A=this.noUpdateTTL}=o;const q=g(this,Er).call(this,n,i,o.size||0,b);if(this.maxEntrySize&&q>this.maxEntrySize)return S&&(S.set="miss",S.maxEntrySizeExceeded=!0),k(this,F,pn).call(this,n,"set"),this;let D=g(this,Ft)===0?void 0:g(this,Et).get(n);if(D===void 0)D=g(this,Ft)===0?g(this,Ot):g(this,Re).length!==0?g(this,Re).pop():g(this,Ft)===g(this,Te)?k(this,F,qs).call(this,!1):g(this,Ft),g(this,mt)[D]=n,g(this,J)[D]=i,g(this,Et).set(n,D),g(this,Zt)[g(this,Ot)]=D,g(this,ce)[D]=g(this,Ot),W(this,Ot,D),Is(this,Ft)._++,g(this,er).call(this,D,q,S),S&&(S.set="add"),A=!1;else{k(this,F,Cr).call(this,D);const z=g(this,J)[D];if(i!==z){if(g(this,_n)&&k(this,F,_t).call(this,z)){z.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:dt}=z;dt===void 0||_||(g(this,De)&&((tt=g(this,Me))==null||tt.call(this,dt,n,"set")),g(this,Yt)&&((et=g(this,Dt))==null||et.push([dt,n,"set"])))}else _||(g(this,De)&&((N=g(this,Me))==null||N.call(this,z,n,"set")),g(this,Yt)&&((rt=g(this,Dt))==null||rt.push([z,n,"set"])));if(g(this,Fn).call(this,D),g(this,er).call(this,D,q,S),g(this,J)[D]=i,S){S.set="replace";const dt=z&&k(this,F,_t).call(this,z)?z.__staleWhileFetching:z;dt!==void 0&&(S.oldValue=dt)}}else S&&(S.set="update")}if(l===0||g(this,le)||k(this,F,wa).call(this),g(this,le)&&(A||g(this,Ir).call(this,D,l,f),S&&g(this,Xe).call(this,S,D)),!_&&g(this,Yt)&&g(this,Dt)){const z=g(this,Dt);let dt;for(;dt=z==null?void 0:z.shift();)(vt=g(this,Ae))==null||vt.call(this,...dt)}return this}pop(){var n;try{for(;g(this,Ft);){const i=g(this,J)[g(this,Gt)];if(k(this,F,qs).call(this,!0),k(this,F,_t).call(this,i)){if(i.__staleWhileFetching)return i.__staleWhileFetching}else if(i!==void 0)return i}}finally{if(g(this,Yt)&&g(this,Dt)){const i=g(this,Dt);let o;for(;o=i==null?void 0:i.shift();)(n=g(this,Ae))==null||n.call(this,...o)}}}has(n,i={}){const{updateAgeOnHas:o=this.updateAgeOnHas,status:l}=i,f=g(this,Et).get(n);if(f!==void 0){const _=g(this,J)[f];if(k(this,F,_t).call(this,_)&&_.__staleWhileFetching===void 0)return!1;if(!g(this,he).call(this,f))return o&&g(this,An).call(this,f),l&&(l.has="hit",g(this,Xe).call(this,l,f)),!0;l&&(l.has="stale",g(this,Xe).call(this,l,f))}else l&&(l.has="miss");return!1}peek(n,i={}){const{allowStale:o=this.allowStale}=i,l=g(this,Et).get(n);if(l===void 0||!o&&g(this,he).call(this,l))return;const f=g(this,J)[l];return k(this,F,_t).call(this,f)?f.__staleWhileFetching:f}async fetch(n,i={}){const{allowStale:o=this.allowStale,updateAgeOnGet:l=this.updateAgeOnGet,noDeleteOnStaleGet:f=this.noDeleteOnStaleGet,ttl:_=this.ttl,noDisposeOnSet:b=this.noDisposeOnSet,size:S=0,sizeCalculation:A=this.sizeCalculation,noUpdateTTL:q=this.noUpdateTTL,noDeleteOnFetchRejection:D=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:tt=this.allowStaleOnFetchRejection,ignoreFetchAbort:et=this.ignoreFetchAbort,allowStaleOnFetchAbort:N=this.allowStaleOnFetchAbort,context:rt,forceRefresh:vt=!1,status:z,signal:dt}=i;if(!g(this,_n))return z&&(z.fetch="get"),this.get(n,{allowStale:o,updateAgeOnGet:l,noDeleteOnStaleGet:f,status:z});const ze={allowStale:o,updateAgeOnGet:l,noDeleteOnStaleGet:f,ttl:_,noDisposeOnSet:b,size:S,sizeCalculation:A,noUpdateTTL:q,noDeleteOnFetchRejection:D,allowStaleOnFetchRejection:tt,allowStaleOnFetchAbort:N,ignoreFetchAbort:et,status:z,signal:dt};let ne=g(this,Et).get(n);if(ne===void 0){z&&(z.fetch="miss");const bt=k(this,F,Os).call(this,n,ne,ze,rt);return bt.__returned=bt}{const bt=g(this,J)[ne];if(k(this,F,_t).call(this,bt)){const ke=o&&bt.__staleWhileFetching!==void 0;return z&&(z.fetch="inflight",ke&&(z.returnedStale=!0)),ke?bt.__staleWhileFetching:bt.__returned=bt}const Ce=g(this,he).call(this,ne);if(!vt&&!Ce)return z&&(z.fetch="hit"),k(this,F,Cr).call(this,ne),l&&g(this,An).call(this,ne),z&&g(this,Xe).call(this,z,ne),bt;const Rt=k(this,F,Os).call(this,n,ne,ze,rt),Ge=Rt.__staleWhileFetching!==void 0&&o;return z&&(z.fetch=Ce?"stale":"refresh",Ge&&Ce&&(z.returnedStale=!0)),Ge?Rt.__staleWhileFetching:Rt.__returned=Rt}}async forceFetch(n,i={}){const o=await this.fetch(n,i);if(o===void 0)throw new Error("fetch() returned undefined");return o}memo(n,i={}){const o=g(this,tr);if(!o)throw new Error("no memoMethod provided to constructor");const{context:l,forceRefresh:f,..._}=i,b=this.get(n,_);if(!f&&b!==void 0)return b;const S=o(n,b,{options:_,context:l});return this.set(n,S,_),S}get(n,i={}){const{allowStale:o=this.allowStale,updateAgeOnGet:l=this.updateAgeOnGet,noDeleteOnStaleGet:f=this.noDeleteOnStaleGet,status:_}=i,b=g(this,Et).get(n);if(b!==void 0){const S=g(this,J)[b],A=k(this,F,_t).call(this,S);return _&&g(this,Xe).call(this,_,b),g(this,he).call(this,b)?(_&&(_.get="stale"),A?(_&&o&&S.__staleWhileFetching!==void 0&&(_.returnedStale=!0),o?S.__staleWhileFetching:void 0):(f||k(this,F,pn).call(this,n,"expire"),_&&o&&(_.returnedStale=!0),o?S:void 0)):(_&&(_.get="hit"),A?S.__staleWhileFetching:(k(this,F,Cr).call(this,b),l&&g(this,An).call(this,b),S))}_&&(_.get="miss")}delete(n){return k(this,F,pn).call(this,n,"delete")}clear(){return k(this,F,Ta).call(this,"delete")}};Te=new WeakMap,ue=new WeakMap,Me=new WeakMap,Ae=new WeakMap,Qn=new WeakMap,tr=new WeakMap,Ft=new WeakMap,Fe=new WeakMap,Et=new WeakMap,mt=new WeakMap,J=new WeakMap,Zt=new WeakMap,ce=new WeakMap,Gt=new WeakMap,Ot=new WeakMap,Re=new WeakMap,Dt=new WeakMap,qe=new WeakMap,Oe=new WeakMap,le=new WeakMap,De=new WeakMap,_n=new WeakMap,Yt=new WeakMap,F=new WeakSet,wa=function(){const n=new Rs(g(this,Te)),i=new Rs(g(this,Te));W(this,le,n),W(this,Oe,i),W(this,Ir,(f,_,b=Vn.now())=>{if(i[f]=_!==0?b:0,n[f]=_,_!==0&&this.ttlAutopurge){const S=setTimeout(()=>{g(this,he).call(this,f)&&k(this,F,pn).call(this,g(this,mt)[f],"expire")},_+1);S.unref&&S.unref()}}),W(this,An,f=>{i[f]=n[f]!==0?Vn.now():0}),W(this,Xe,(f,_)=>{if(n[_]){const b=n[_],S=i[_];if(!b||!S)return;f.ttl=b,f.start=S,f.now=o||l();const A=f.now-S;f.remainingTTL=b-A}});let o=0;const l=()=>{const f=Vn.now();if(this.ttlResolution>0){o=f;const _=setTimeout(()=>o=0,this.ttlResolution);_.unref&&_.unref()}return f};this.getRemainingTTL=f=>{const _=g(this,Et).get(f);if(_===void 0)return 0;const b=n[_],S=i[_];return!b||!S?1/0:b-((o||l())-S)},W(this,he,f=>{const _=i[f],b=n[f];return!!b&&!!_&&(o||l())-_>b})},An=new WeakMap,Xe=new WeakMap,Ir=new WeakMap,he=new WeakMap,Vc=function(){const n=new Rs(g(this,Te));W(this,Fe,0),W(this,qe,n),W(this,Fn,i=>{W(this,Fe,g(this,Fe)-n[i]),n[i]=0}),W(this,Er,(i,o,l,f)=>{if(k(this,F,_t).call(this,o))return 0;if(!dn(l)){if(!f)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if(typeof f!="function")throw new TypeError("sizeCalculation must be a function");if(l=f(o,i),!dn(l))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return l}),W(this,er,(i,o,l)=>{if(n[i]=o,g(this,ue)){const f=g(this,ue)-n[i];for(;g(this,Fe)>f;)k(this,F,qs).call(this,!0)}W(this,Fe,g(this,Fe)+n[i]),l&&(l.entrySize=o,l.totalCalculatedSize=g(this,Fe))})},Fn=new WeakMap,er=new WeakMap,Er=new WeakMap,fn=function*({allowStale:n=this.allowStale}={}){if(g(this,Ft))for(let i=g(this,Ot);k(this,F,Ia).call(this,i)&&(!n&&g(this,he).call(this,i)||(yield i),i!==g(this,Gt));)i=g(this,ce)[i]},gn=function*({allowStale:n=this.allowStale}={}){if(g(this,Ft))for(let i=g(this,Gt);k(this,F,Ia).call(this,i)&&(!n&&g(this,he).call(this,i)||(yield i),i!==g(this,Ot));)i=g(this,Zt)[i]},Ia=function(n){return n!==void 0&&g(this,Et).get(g(this,mt)[n])===n},qs=function(n){var f,_;const i=g(this,Gt),o=g(this,mt)[i],l=g(this,J)[i];return g(this,_n)&&k(this,F,_t).call(this,l)?l.__abortController.abort(new Error("evicted")):(g(this,De)||g(this,Yt))&&(g(this,De)&&((f=g(this,Me))==null||f.call(this,l,o,"evict")),g(this,Yt)&&((_=g(this,Dt))==null||_.push([l,o,"evict"]))),g(this,Fn).call(this,i),n&&(g(this,mt)[i]=void 0,g(this,J)[i]=void 0,g(this,Re).push(i)),g(this,Ft)===1?(W(this,Gt,W(this,Ot,0)),g(this,Re).length=0):W(this,Gt,g(this,Zt)[i]),g(this,Et).delete(o),Is(this,Ft)._--,i},Os=function(n,i,o,l){const f=i===void 0?void 0:g(this,J)[i];if(k(this,F,_t).call(this,f))return f;const _=new ks,{signal:b}=o;b==null||b.addEventListener("abort",()=>_.abort(b.reason),{signal:_.signal});const S={signal:_.signal,options:o,context:l},A=(et,N=!1)=>{const{aborted:rt}=_.signal,vt=o.ignoreFetchAbort&&et!==void 0;if(o.status&&(rt&&!N?(o.status.fetchAborted=!0,o.status.fetchError=_.signal.reason,vt&&(o.status.fetchAbortIgnored=!0)):o.status.fetchResolved=!0),rt&&!vt&&!N)return q(_.signal.reason);const z=D;return g(this,J)[i]===D&&(et===void 0?z.__staleWhileFetching?g(this,J)[i]=z.__staleWhileFetching:k(this,F,pn).call(this,n,"fetch"):(o.status&&(o.status.fetchUpdated=!0),this.set(n,et,S.options))),et},q=et=>{const{aborted:N}=_.signal,rt=N&&o.allowStaleOnFetchAbort,vt=rt||o.allowStaleOnFetchRejection,z=vt||o.noDeleteOnFetchRejection,dt=D;if(g(this,J)[i]===D&&(!z||dt.__staleWhileFetching===void 0?k(this,F,pn).call(this,n,"fetch"):rt||(g(this,J)[i]=dt.__staleWhileFetching)),vt)return o.status&&dt.__staleWhileFetching!==void 0&&(o.status.returnedStale=!0),dt.__staleWhileFetching;if(dt.__returned===dt)throw et};o.status&&(o.status.fetchDispatched=!0);const D=new Promise((et,N)=>{var vt;const rt=(vt=g(this,Qn))==null?void 0:vt.call(this,n,f,S);rt&&rt instanceof Promise&&rt.then(z=>et(z===void 0?void 0:z),N),_.signal.addEventListener("abort",()=>{o.ignoreFetchAbort&&!o.allowStaleOnFetchAbort||(et(void 0),o.allowStaleOnFetchAbort&&(et=z=>A(z,!0)))})}).then(A,et=>(o.status&&(o.status.fetchRejected=!0,o.status.fetchError=et),q(et))),tt=Object.assign(D,{__abortController:_,__staleWhileFetching:f,__returned:void 0});return i===void 0?(this.set(n,tt,{...S.options,status:void 0}),i=g(this,Et).get(n)):g(this,J)[i]=tt,tt},_t=function(n){if(!g(this,_n))return!1;const i=n;return!!i&&i instanceof Promise&&i.hasOwnProperty("__staleWhileFetching")&&i.__abortController instanceof ks},Ea=function(n,i){g(this,ce)[i]=n,g(this,Zt)[n]=i},Cr=function(n){n!==g(this,Ot)&&(n===g(this,Gt)?W(this,Gt,g(this,Zt)[n]):k(this,F,Ea).call(this,g(this,ce)[n],g(this,Zt)[n]),k(this,F,Ea).call(this,g(this,Ot),n),W(this,Ot,n))},pn=function(n,i){var l,f,_,b;let o=!1;if(g(this,Ft)!==0){const S=g(this,Et).get(n);if(S!==void 0)if(o=!0,g(this,Ft)===1)k(this,F,Ta).call(this,i);else{g(this,Fn).call(this,S);const A=g(this,J)[S];if(k(this,F,_t).call(this,A)?A.__abortController.abort(new Error("deleted")):(g(this,De)||g(this,Yt))&&(g(this,De)&&((l=g(this,Me))==null||l.call(this,A,n,i)),g(this,Yt)&&((f=g(this,Dt))==null||f.push([A,n,i]))),g(this,Et).delete(n),g(this,mt)[S]=void 0,g(this,J)[S]=void 0,S===g(this,Ot))W(this,Ot,g(this,ce)[S]);else if(S===g(this,Gt))W(this,Gt,g(this,Zt)[S]);else{const q=g(this,ce)[S];g(this,Zt)[q]=g(this,Zt)[S];const D=g(this,Zt)[S];g(this,ce)[D]=g(this,ce)[S]}Is(this,Ft)._--,g(this,Re).push(S)}}if(g(this,Yt)&&((_=g(this,Dt))!=null&&_.length)){const S=g(this,Dt);let A;for(;A=S==null?void 0:S.shift();)(b=g(this,Ae))==null||b.call(this,...A)}return o},Ta=function(n){var i,o,l;for(const f of k(this,F,gn).call(this,{allowStale:!0})){const _=g(this,J)[f];if(k(this,F,_t).call(this,_))_.__abortController.abort(new Error("deleted"));else{const b=g(this,mt)[f];g(this,De)&&((i=g(this,Me))==null||i.call(this,_,b,n)),g(this,Yt)&&((o=g(this,Dt))==null||o.push([_,b,n]))}}if(g(this,Et).clear(),g(this,J).fill(void 0),g(this,mt).fill(void 0),g(this,le)&&g(this,Oe)&&(g(this,le).fill(0),g(this,Oe).fill(0)),g(this,qe)&&g(this,qe).fill(0),W(this,Gt,0),W(this,Ot,0),g(this,Re).length=0,W(this,Fe,0),W(this,Ft,0),g(this,Yt)&&g(this,Dt)){const f=g(this,Dt);let _;for(;_=f==null?void 0:f.shift();)(l=g(this,Ae))==null||l.call(this,..._)}};let xa=qa;class Cg{constructor(){d(this,"_syncStatus",{status:rf.done,foldersProgress:[]});d(this,"_syncEnabledState",pc.initializing);d(this,"_workspaceGuidelines",[]);d(this,"_openUserGuidelinesInput",!1);d(this,"_userGuidelines");d(this,"_contextStore",new Rf);d(this,"_prevOpenFiles",[]);d(this,"_disableContext",!1);d(this,"_enableAgentMemories",!1);d(this,"subscribers",new Set);d(this,"subscribe",n=>(this.subscribers.add(n),n(this),()=>{this.subscribers.delete(n)}));d(this,"handleMessageFromExtension",n=>{const i=n.data;switch(i.type){case U.sourceFoldersUpdated:this.onSourceFoldersUpdated(i.data.sourceFolders);break;case U.sourceFoldersSyncStatus:this.onSyncStatusUpdated(i.data);break;case U.fileRangesSelected:this.updateSelections(i.data);break;case U.currentlyOpenFiles:this.setCurrentlyOpenFiles(i.data);break;case U.syncEnabledState:this.onSyncEnabledStateUpdate(i.data);break;case U.updateGuidelinesState:this.onGuidelinesStateUpdate(i.data);break;default:return!1}return!0});d(this,"onSourceFoldersUpdated",n=>{const i=this.sourceFolders;n=this.updateSourceFoldersWithGuidelines(n),this._contextStore.update(n.map(o=>({sourceFolder:o,status:kt.active,label:o.folderRoot,showWarning:o.guidelinesOverLimit,id:o.folderRoot+String(o.guidelinesEnabled)+String(o.guidelinesOverLimit)})),i,o=>o.id),this.notifySubscribers()});d(this,"onSyncStatusUpdated",n=>{this._syncStatus=n,this.notifySubscribers()});d(this,"disableContext",()=>{this._disableContext=!0,this.notifySubscribers()});d(this,"enableContext",()=>{this._disableContext=!1,this.notifySubscribers()});d(this,"addFile",n=>{this.addFiles([n])});d(this,"addFiles",n=>{this.updateFiles(n,[])});d(this,"removeFile",n=>{this.removeFiles([n])});d(this,"removeFiles",n=>{this.updateFiles([],n)});d(this,"updateItems",(n,i)=>{this.updateItemsInplace(n,i),this.notifySubscribers()});d(this,"updateItemsInplace",(n,i)=>{this._contextStore.update(n,i,o=>o.id)});d(this,"updateFiles",(n,i)=>{const o=_=>({file:_,...Fs(_)}),l=n.map(o),f=i.map(o);this._contextStore.update(l,f,_=>_.id),this.notifySubscribers()});d(this,"enableAgentMemories",()=>{this._enableAgentMemories=!0,this.notifySubscribers()});d(this,"disableAgentMemories",()=>{this._enableAgentMemories=!1,this.notifySubscribers()});d(this,"setCurrentlyOpenFiles",n=>{const i=n.map(l=>({recentFile:l,...Fs(l)})),o=this._prevOpenFiles;this._prevOpenFiles=i,this._contextStore.update(i,o,l=>l.id),o.forEach(l=>{const f=this._contextStore.peekKey(l.id);f!=null&&f.recentFile&&(f.file=f.recentFile,delete f.recentFile)}),i.forEach(l=>{const f=this._contextStore.peekKey(l.id);f!=null&&f.file&&(f.recentFile=f.file,delete f.file)}),this.notifySubscribers()});d(this,"onSyncEnabledStateUpdate",n=>{this._syncEnabledState=n,this.notifySubscribers()});d(this,"updateUserGuidelines",n=>{const i=this.userGuidelines,o={userGuidelines:n,label:"User Guidelines",id:"userGuidelines",status:kt.active,referenceCount:1,showWarning:n.overLimit};this._contextStore.update([o],i,l=>{var f,_;return l.id+String((f=l.userGuidelines)==null?void 0:f.enabled)+String((_=l.userGuidelines)==null?void 0:_.overLimit)}),this.notifySubscribers()});d(this,"onGuidelinesStateUpdate",n=>{this._userGuidelines=n.userGuidelines,this._workspaceGuidelines=n.workspaceGuidelines??[];const i=n.userGuidelines;i&&this.updateUserGuidelines(i),this.onSourceFoldersUpdated(this.sourceFolders.map(o=>o.sourceFolder))});d(this,"updateSourceFoldersWithGuidelines",n=>n.map(i=>{const o=this._workspaceGuidelines.find(l=>l.workspaceFolder===i.folderRoot);return{...i,guidelinesEnabled:(o==null?void 0:o.enabled)??!1,guidelinesOverLimit:(o==null?void 0:o.overLimit)??!1,guidelinesLengthLimit:(o==null?void 0:o.lengthLimit)??2e3}}));d(this,"toggleStatus",n=>{this._contextStore.toggleStatus(n.id),this.notifySubscribers()});d(this,"updateExternalSources",(n,i)=>{this._contextStore.update(n,i,o=>o.id),this.notifySubscribers()});d(this,"clearFiles",()=>{this._contextStore.update([],this.files,n=>n.id),this.notifySubscribers()});d(this,"updateSelections",n=>{const i=this._contextStore.values.filter(_c);this._contextStore.update(n.map(o=>({selection:o,...Fs(o)})),i,o=>o.id),this.notifySubscribers()});d(this,"maybeHandleDelete",({editor:n})=>{if(n.state.selection.empty&&n.state.selection.$anchor.pos===1&&this.recentActiveItems.length>0){const i=this.recentActiveItems[0];return this.markInactive(i),!0}return!1});d(this,"markInactive",n=>{this.markItemsInactive([n])});d(this,"markItemsInactive",n=>{n.forEach(i=>{this._contextStore.setStatus(i.id,kt.inactive)}),this.notifySubscribers()});d(this,"markAllInactive",()=>{this.markItemsInactive(this.recentActiveItems)});d(this,"markActive",n=>{this.markItemsActive([n])});d(this,"markItemsActive",n=>{n.forEach(i=>{this._contextStore.setStatus(i.id,kt.active)}),this.notifySubscribers()});d(this,"markAllActive",()=>{this.markItemsActive(this.recentInactiveItems)});d(this,"unpin",n=>{this._contextStore.unpin(n.id),this.notifySubscribers()});d(this,"togglePinned",n=>{this._contextStore.togglePinned(n.id),this.notifySubscribers()});d(this,"notifySubscribers",()=>{this.subscribers.forEach(n=>n(this))});this.clearFiles()}get files(){return this._disableContext?[]:this._contextStore.values.filter(n=>Pc(n)&&!va(n))}get recentFiles(){return this._disableContext?[]:this._contextStore.values.filter(va)}get userGuidelinesText(){var n;return((n=this._userGuidelines)==null?void 0:n.contents)??""}get selections(){return this._disableContext?[]:this._contextStore.values.filter(_c)}get folders(){return this._disableContext?[]:this._contextStore.values.filter(Nc)}get sourceFolders(){return this._disableContext?[]:this._contextStore.values.filter(ya)}get externalSources(){return this._disableContext?[]:this._contextStore.values.filter(Uc)}get userGuidelines(){return this._contextStore.values.filter(mc)}get agentMemories(){return[{...cf,status:this._enableAgentMemories?kt.active:kt.inactive,referenceCount:1}]}get activeFiles(){return this._disableContext?[]:this.files.filter(n=>n.status===kt.active)}get activeRecentFiles(){return this._disableContext?[]:this.recentFiles.filter(n=>n.status===kt.active)}get activeExternalSources(){return this._disableContext?[]:this.externalSources.filter(n=>n.status===kt.active)}get activeSelections(){return this._disableContext?[]:this.selections.filter(n=>n.status===kt.active)}get activeSourceFolders(){return this._disableContext?[]:this.sourceFolders.filter(n=>n.status===kt.active)}get syncStatus(){return this._syncStatus.status}get syncEnabledState(){return this._syncEnabledState}get syncProgress(){var S;if(this.syncEnabledState===pc.disabled||!this._syncStatus.foldersProgress)return;const n=this._syncStatus.foldersProgress.filter(A=>A.progress!==void 0);if(n.length===0)return;const i=n.reduce((A,q)=>{var D;return A+(((D=q==null?void 0:q.progress)==null?void 0:D.trackedFiles)??0)},0),o=n.reduce((A,q)=>{var D;return A+(((D=q==null?void 0:q.progress)==null?void 0:D.backlogSize)??0)},0),l=Math.max(i,0),f=Math.min(Math.max(o,0),l),_=l-f,b=[];for(const A of n)(S=A==null?void 0:A.progress)!=null&&S.newlyTracked&&b.push(A.folderRoot);return{status:this._syncStatus.status,totalFiles:l,syncedCount:_,backlogSize:f,newlyTrackedFolders:b}}get contextCounts(){return this._contextStore.values.length??0}get chatActiveContext(){return{userSpecifiedFiles:this.activeFiles.map(n=>({rootPath:n.file.repoRoot,relPath:n.file.pathName})),recentFiles:this.activeRecentFiles.map(n=>({rootPath:n.recentFile.repoRoot,relPath:n.recentFile.pathName})),externalSources:this.activeExternalSources.map(n=>n.externalSource),selections:this.activeSelections.map(n=>n.selection),sourceFolders:this.activeSourceFolders.map(n=>({rootPath:n.sourceFolder.folderRoot,relPath:""}))}}get recentItems(){return this._disableContext?this.userGuidelines:[...this._contextStore.values.filter(n=>!ya(n)&&!mc(n)&&!Aa(n)),...this.sourceFolders,...this.userGuidelines,...this.agentMemories]}get recentActiveItems(){return this.recentItems.filter(n=>n.status===kt.active)}get recentInactiveItems(){return this.recentItems.filter(n=>n.status===kt.inactive)}get isContextDisabled(){return this._disableContext}}class Rf{constructor(){d(this,"_cache",new xa({max:1e3}));d(this,"peekKey",n=>this._cache.get(n,{updateAgeOnGet:!1}));d(this,"clear",()=>{this._cache.clear()});d(this,"update",(n,i,o)=>{n.forEach(l=>this.addInPlace(l,o)),i.forEach(l=>this.removeInPlace(l,o))});d(this,"removeFromStore",(n,i)=>{const o=i(n);this._cache.delete(o)});d(this,"addInPlace",(n,i)=>{const o=i(n),l=n.referenceCount??1,f=this._cache.get(o),_=n.status??(f==null?void 0:f.status)??kt.active;f?(f.referenceCount+=l,f.status=_,f.pinned=n.pinned??f.pinned,f.showWarning=n.showWarning??f.showWarning):this._cache.set(o,{...n,pinned:void 0,referenceCount:l,status:_})});d(this,"removeInPlace",(n,i)=>{const o=i(n),l=this._cache.get(o);l&&(l.referenceCount-=1,l.referenceCount===0&&this._cache.delete(o))});d(this,"setStatus",(n,i)=>{const o=this._cache.get(n);o&&(o.status=i)});d(this,"togglePinned",n=>{const i=this._cache.peek(n);i&&(i.pinned?this.unpin(n):this.pin(n))});d(this,"pin",n=>{const i=this._cache.peek(n);i&&!i.pinned&&(i.pinned=!0,i.referenceCount+=1)});d(this,"unpin",n=>{const i=this._cache.peek(n);i&&i.pinned&&(i.pinned=!1,i.referenceCount-=1,i.referenceCount===0&&this._cache.delete(n))});d(this,"toggleStatus",n=>{const i=this._cache.get(n);i&&(i.status=i.status===kt.active?kt.inactive:kt.active)})}get store(){return Object.fromEntries(this._cache.entries())}get values(){return[...this._cache.values()]}}function qf(s){let n,i;return{c(){n=nr("svg"),i=nr("path"),ct(i,"fill-rule","evenodd"),ct(i,"clip-rule","evenodd"),ct(i,"d","M5 2V1H10V2H5ZM4.75 0C4.33579 0 4 0.335786 4 0.75V1H3.5C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H7V13H3.5C3.22386 13 3 12.7761 3 12.5V2.5C3 2.22386 3.22386 2 3.5 2H4V2.25C4 2.66421 4.33579 3 4.75 3H10.25C10.6642 3 11 2.66421 11 2.25V2H11.5C11.7761 2 12 2.22386 12 2.5V7H13V2.5C13 1.67157 12.3284 1 11.5 1H11V0.75C11 0.335786 10.6642 0 10.25 0H4.75ZM9 8.5C9 8.77614 8.77614 9 8.5 9C8.22386 9 8 8.77614 8 8.5C8 8.22386 8.22386 8 8.5 8C8.77614 8 9 8.22386 9 8.5ZM10.5 9C10.7761 9 11 8.77614 11 8.5C11 8.22386 10.7761 8 10.5 8C10.2239 8 10 8.22386 10 8.5C10 8.77614 10.2239 9 10.5 9ZM13 8.5C13 8.77614 12.7761 9 12.5 9C12.2239 9 12 8.77614 12 8.5C12 8.22386 12.2239 8 12.5 8C12.7761 8 13 8.22386 13 8.5ZM14.5 9C14.7761 9 15 8.77614 15 8.5C15 8.22386 14.7761 8 14.5 8C14.2239 8 14 8.22386 14 8.5C14 8.77614 14.2239 9 14.5 9ZM15 10.5C15 10.7761 14.7761 11 14.5 11C14.2239 11 14 10.7761 14 10.5C14 10.2239 14.2239 10 14.5 10C14.7761 10 15 10.2239 15 10.5ZM14.5 13C14.7761 13 15 12.7761 15 12.5C15 12.2239 14.7761 12 14.5 12C14.2239 12 14 12.2239 14 12.5C14 12.7761 14.2239 13 14.5 13ZM14.5 15C14.7761 15 15 14.7761 15 14.5C15 14.2239 14.7761 14 14.5 14C14.2239 14 14 14.2239 14 14.5C14 14.7761 14.2239 15 14.5 15ZM8.5 11C8.77614 11 9 10.7761 9 10.5C9 10.2239 8.77614 10 8.5 10C8.22386 10 8 10.2239 8 10.5C8 10.7761 8.22386 11 8.5 11ZM9 12.5C9 12.7761 8.77614 13 8.5 13C8.22386 13 8 12.7761 8 12.5C8 12.2239 8.22386 12 8.5 12C8.77614 12 9 12.2239 9 12.5ZM8.5 15C8.77614 15 9 14.7761 9 14.5C9 14.2239 8.77614 14 8.5 14C8.22386 14 8 14.2239 8 14.5C8 14.7761 8.22386 15 8.5 15ZM11 14.5C11 14.7761 10.7761 15 10.5 15C10.2239 15 10 14.7761 10 14.5C10 14.2239 10.2239 14 10.5 14C10.7761 14 11 14.2239 11 14.5ZM12.5 15C12.7761 15 13 14.7761 13 14.5C13 14.2239 12.7761 14 12.5 14C12.2239 14 12 14.2239 12 14.5C12 14.7761 12.2239 15 12.5 15Z"),ct(i,"fill","currentColor"),ct(n,"width","15"),ct(n,"height","15"),ct(n,"viewBox","0 0 15 15"),ct(n,"fill","none"),ct(n,"xmlns","http://www.w3.org/2000/svg")},m(o,l){Tr(o,n,l),je(n,i)},p:tn,i:tn,o:tn,d(o){o&&Mr(n)}}}class xg extends Ps{constructor(n){super(),Ns(this,n,null,qf,Us,{})}}var Ms,As,Ma={exports:{}};Ms=Ma,As=Ma.exports,(function(){var s,n="Expected a function",i="__lodash_hash_undefined__",o="__lodash_placeholder__",l=16,f=32,_=64,b=128,S=256,A=1/0,q=9007199254740991,D=NaN,tt=4294967295,et=[["ary",b],["bind",1],["bindKey",2],["curry",8],["curryRight",l],["flip",512],["partial",f],["partialRight",_],["rearg",S]],N="[object Arguments]",rt="[object Array]",vt="[object Boolean]",z="[object Date]",dt="[object Error]",ze="[object Function]",ne="[object GeneratorFunction]",bt="[object Map]",Ce="[object Number]",Rt="[object Object]",Ge="[object Promise]",ke="[object RegExp]",xe="[object Set]",rr="[object String]",Ar="[object Symbol]",sr="[object WeakMap]",ir="[object ArrayBuffer]",qn="[object DataView]",Hs="[object Float32Array]",js="[object Float64Array]",zs="[object Int8Array]",Gs="[object Int16Array]",Ws="[object Int32Array]",Bs="[object Uint8Array]",$s="[object Uint8ClampedArray]",Vs="[object Uint16Array]",Zs="[object Uint32Array]",Zc=/\b__p \+= '';/g,Yc=/\b(__p \+=) '' \+/g,Jc=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Oa=/&(?:amp|lt|gt|quot|#39);/g,Da=/[&<>"']/g,Kc=RegExp(Oa.source),Xc=RegExp(Da.source),Qc=/<%-([\s\S]+?)%>/g,tl=/<%([\s\S]+?)%>/g,ka=/<%=([\s\S]+?)%>/g,el=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,nl=/^\w*$/,rl=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Ys=/[\\^$.*+?()[\]{}|]/g,sl=RegExp(Ys.source),Js=/^\s+/,il=/\s/,al=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,ol=/\{\n\/\* \[wrapped with (.+)\] \*/,ul=/,? & /,cl=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,ll=/[()=,{}\[\]\/\s]/,hl=/\\(\\)?/g,dl=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,Pa=/\w*$/,fl=/^[-+]0x[0-9a-f]+$/i,gl=/^0b[01]+$/i,pl=/^\[object .+?Constructor\]$/,_l=/^0o[0-7]+$/i,ml=/^(?:0|[1-9]\d*)$/,vl=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Fr=/($^)/,yl=/['\n\r\u2028\u2029\\]/g,Rr="\\ud800-\\udfff",Na="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",Ua="\\u2700-\\u27bf",La="a-z\\xdf-\\xf6\\xf8-\\xff",Ha="A-Z\\xc0-\\xd6\\xd8-\\xde",ja="\\ufe0e\\ufe0f",za="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",bl="['’]",Sl="["+Rr+"]",Ga="["+za+"]",qr="["+Na+"]",Wa="\\d+",Cl="["+Ua+"]",Ba="["+La+"]",$a="[^"+Rr+za+Wa+Ua+La+Ha+"]",Ks="\\ud83c[\\udffb-\\udfff]",Va="[^"+Rr+"]",Xs="(?:\\ud83c[\\udde6-\\uddff]){2}",Qs="[\\ud800-\\udbff][\\udc00-\\udfff]",On="["+Ha+"]",Za="\\u200d",Ya="(?:"+Ba+"|"+$a+")",xl="(?:"+On+"|"+$a+")",Ja="(?:['’](?:d|ll|m|re|s|t|ve))?",Ka="(?:['’](?:D|LL|M|RE|S|T|VE))?",Xa="(?:"+qr+"|"+Ks+")?",Qa="["+ja+"]?",to=Qa+Xa+"(?:"+Za+"(?:"+[Va,Xs,Qs].join("|")+")"+Qa+Xa+")*",wl="(?:"+[Cl,Xs,Qs].join("|")+")"+to,Il="(?:"+[Va+qr+"?",qr,Xs,Qs,Sl].join("|")+")",El=RegExp(bl,"g"),Tl=RegExp(qr,"g"),ti=RegExp(Ks+"(?="+Ks+")|"+Il+to,"g"),Ml=RegExp([On+"?"+Ba+"+"+Ja+"(?="+[Ga,On,"$"].join("|")+")",xl+"+"+Ka+"(?="+[Ga,On+Ya,"$"].join("|")+")",On+"?"+Ya+"+"+Ja,On+"+"+Ka,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Wa,wl].join("|"),"g"),Al=RegExp("["+Za+Rr+Na+ja+"]"),Fl=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,Rl=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],ql=-1,ft={};ft[Hs]=ft[js]=ft[zs]=ft[Gs]=ft[Ws]=ft[Bs]=ft[$s]=ft[Vs]=ft[Zs]=!0,ft[N]=ft[rt]=ft[ir]=ft[vt]=ft[qn]=ft[z]=ft[dt]=ft[ze]=ft[bt]=ft[Ce]=ft[Rt]=ft[ke]=ft[xe]=ft[rr]=ft[sr]=!1;var ht={};ht[N]=ht[rt]=ht[ir]=ht[qn]=ht[vt]=ht[z]=ht[Hs]=ht[js]=ht[zs]=ht[Gs]=ht[Ws]=ht[bt]=ht[Ce]=ht[Rt]=ht[ke]=ht[xe]=ht[rr]=ht[Ar]=ht[Bs]=ht[$s]=ht[Vs]=ht[Zs]=!0,ht[dt]=ht[ze]=ht[sr]=!1;var Ol={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Dl=parseFloat,kl=parseInt,eo=typeof Sr=="object"&&Sr&&Sr.Object===Object&&Sr,Pl=typeof self=="object"&&self&&self.Object===Object&&self,Nt=eo||Pl||Function("return this")(),ei=As&&!As.nodeType&&As,mn=ei&&Ms&&!Ms.nodeType&&Ms,no=mn&&mn.exports===ei,ni=no&&eo.process,ge=function(){try{var y=mn&&mn.require&&mn.require("util").types;return y||ni&&ni.binding&&ni.binding("util")}catch{}}(),ro=ge&&ge.isArrayBuffer,so=ge&&ge.isDate,io=ge&&ge.isMap,ao=ge&&ge.isRegExp,oo=ge&&ge.isSet,uo=ge&&ge.isTypedArray;function re(y,I,E){switch(E.length){case 0:return y.call(I);case 1:return y.call(I,E[0]);case 2:return y.call(I,E[0],E[1]);case 3:return y.call(I,E[0],E[1],E[2])}return y.apply(I,E)}function Nl(y,I,E,O){for(var Z=-1,st=y==null?0:y.length;++Z<st;){var Tt=y[Z];I(O,Tt,E(Tt),y)}return O}function pe(y,I){for(var E=-1,O=y==null?0:y.length;++E<O&&I(y[E],E,y)!==!1;);return y}function Ul(y,I){for(var E=y==null?0:y.length;E--&&I(y[E],E,y)!==!1;);return y}function co(y,I){for(var E=-1,O=y==null?0:y.length;++E<O;)if(!I(y[E],E,y))return!1;return!0}function en(y,I){for(var E=-1,O=y==null?0:y.length,Z=0,st=[];++E<O;){var Tt=y[E];I(Tt,E,y)&&(st[Z++]=Tt)}return st}function Or(y,I){return!(y==null||!y.length)&&Dn(y,I,0)>-1}function ri(y,I,E){for(var O=-1,Z=y==null?0:y.length;++O<Z;)if(E(I,y[O]))return!0;return!1}function yt(y,I){for(var E=-1,O=y==null?0:y.length,Z=Array(O);++E<O;)Z[E]=I(y[E],E,y);return Z}function nn(y,I){for(var E=-1,O=I.length,Z=y.length;++E<O;)y[Z+E]=I[E];return y}function si(y,I,E,O){var Z=-1,st=y==null?0:y.length;for(O&&st&&(E=y[++Z]);++Z<st;)E=I(E,y[Z],Z,y);return E}function Ll(y,I,E,O){var Z=y==null?0:y.length;for(O&&Z&&(E=y[--Z]);Z--;)E=I(E,y[Z],Z,y);return E}function ii(y,I){for(var E=-1,O=y==null?0:y.length;++E<O;)if(I(y[E],E,y))return!0;return!1}var Hl=ai("length");function lo(y,I,E){var O;return E(y,function(Z,st,Tt){if(I(Z,st,Tt))return O=st,!1}),O}function Dr(y,I,E,O){for(var Z=y.length,st=E+(O?1:-1);O?st--:++st<Z;)if(I(y[st],st,y))return st;return-1}function Dn(y,I,E){return I==I?function(O,Z,st){for(var Tt=st-1,Pe=O.length;++Tt<Pe;)if(O[Tt]===Z)return Tt;return-1}(y,I,E):Dr(y,ho,E)}function jl(y,I,E,O){for(var Z=E-1,st=y.length;++Z<st;)if(O(y[Z],I))return Z;return-1}function ho(y){return y!=y}function fo(y,I){var E=y==null?0:y.length;return E?ui(y,I)/E:D}function ai(y){return function(I){return I==null?s:I[y]}}function oi(y){return function(I){return y==null?s:y[I]}}function go(y,I,E,O,Z){return Z(y,function(st,Tt,Pe){E=O?(O=!1,st):I(E,st,Tt,Pe)}),E}function ui(y,I){for(var E,O=-1,Z=y.length;++O<Z;){var st=I(y[O]);st!==s&&(E=E===s?st:E+st)}return E}function ci(y,I){for(var E=-1,O=Array(y);++E<y;)O[E]=I(E);return O}function po(y){return y&&y.slice(0,yo(y)+1).replace(Js,"")}function se(y){return function(I){return y(I)}}function li(y,I){return yt(I,function(E){return y[E]})}function ar(y,I){return y.has(I)}function _o(y,I){for(var E=-1,O=y.length;++E<O&&Dn(I,y[E],0)>-1;);return E}function mo(y,I){for(var E=y.length;E--&&Dn(I,y[E],0)>-1;);return E}var zl=oi({À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"}),Gl=oi({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function Wl(y){return"\\"+Ol[y]}function kn(y){return Al.test(y)}function hi(y){var I=-1,E=Array(y.size);return y.forEach(function(O,Z){E[++I]=[Z,O]}),E}function vo(y,I){return function(E){return y(I(E))}}function rn(y,I){for(var E=-1,O=y.length,Z=0,st=[];++E<O;){var Tt=y[E];Tt!==I&&Tt!==o||(y[E]=o,st[Z++]=E)}return st}function kr(y){var I=-1,E=Array(y.size);return y.forEach(function(O){E[++I]=O}),E}function Bl(y){var I=-1,E=Array(y.size);return y.forEach(function(O){E[++I]=[O,O]}),E}function Pn(y){return kn(y)?function(I){for(var E=ti.lastIndex=0;ti.test(I);)++E;return E}(y):Hl(y)}function we(y){return kn(y)?function(I){return I.match(ti)||[]}(y):function(I){return I.split("")}(y)}function yo(y){for(var I=y.length;I--&&il.test(y.charAt(I)););return I}var $l=oi({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),Nn=function y(I){var E,O=(I=I==null?Nt:Nn.defaults(Nt.Object(),I,Nn.pick(Nt,Rl))).Array,Z=I.Date,st=I.Error,Tt=I.Function,Pe=I.Math,gt=I.Object,di=I.RegExp,Vl=I.String,_e=I.TypeError,Pr=O.prototype,Zl=Tt.prototype,Un=gt.prototype,Nr=I["__core-js_shared__"],Ur=Zl.toString,lt=Un.hasOwnProperty,Yl=0,bo=(E=/[^.]+$/.exec(Nr&&Nr.keys&&Nr.keys.IE_PROTO||""))?"Symbol(src)_1."+E:"",Lr=Un.toString,Jl=Ur.call(gt),Kl=Nt._,Xl=di("^"+Ur.call(lt).replace(Ys,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Hr=no?I.Buffer:s,sn=I.Symbol,jr=I.Uint8Array,So=Hr?Hr.allocUnsafe:s,zr=vo(gt.getPrototypeOf,gt),Co=gt.create,xo=Un.propertyIsEnumerable,Gr=Pr.splice,wo=sn?sn.isConcatSpreadable:s,or=sn?sn.iterator:s,vn=sn?sn.toStringTag:s,Wr=function(){try{var t=xn(gt,"defineProperty");return t({},"",{}),t}catch{}}(),Ql=I.clearTimeout!==Nt.clearTimeout&&I.clearTimeout,th=Z&&Z.now!==Nt.Date.now&&Z.now,eh=I.setTimeout!==Nt.setTimeout&&I.setTimeout,Br=Pe.ceil,$r=Pe.floor,fi=gt.getOwnPropertySymbols,nh=Hr?Hr.isBuffer:s,Io=I.isFinite,rh=Pr.join,sh=vo(gt.keys,gt),Mt=Pe.max,Ht=Pe.min,ih=Z.now,ah=I.parseInt,Eo=Pe.random,oh=Pr.reverse,gi=xn(I,"DataView"),ur=xn(I,"Map"),pi=xn(I,"Promise"),Ln=xn(I,"Set"),cr=xn(I,"WeakMap"),lr=xn(gt,"create"),Vr=cr&&new cr,Hn={},uh=wn(gi),ch=wn(ur),lh=wn(pi),hh=wn(Ln),dh=wn(cr),Zr=sn?sn.prototype:s,hr=Zr?Zr.valueOf:s,To=Zr?Zr.toString:s;function c(t){if(Ct(t)&&!K(t)&&!(t instanceof nt)){if(t instanceof me)return t;if(lt.call(t,"__wrapped__"))return Mu(t)}return new me(t)}var jn=function(){function t(){}return function(e){if(!St(e))return{};if(Co)return Co(e);t.prototype=e;var r=new t;return t.prototype=s,r}}();function Yr(){}function me(t,e){this.__wrapped__=t,this.__actions__=[],this.__chain__=!!e,this.__index__=0,this.__values__=s}function nt(t){this.__wrapped__=t,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=tt,this.__views__=[]}function yn(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var a=t[e];this.set(a[0],a[1])}}function We(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var a=t[e];this.set(a[0],a[1])}}function Be(t){var e=-1,r=t==null?0:t.length;for(this.clear();++e<r;){var a=t[e];this.set(a[0],a[1])}}function bn(t){var e=-1,r=t==null?0:t.length;for(this.__data__=new Be;++e<r;)this.add(t[e])}function Ie(t){var e=this.__data__=new We(t);this.size=e.size}function Mo(t,e){var r=K(t),a=!r&&In(t),u=!r&&!a&&ln(t),h=!r&&!a&&!u&&Bn(t),p=r||a||u||h,m=p?ci(t.length,Vl):[],v=m.length;for(var x in t)!e&&!lt.call(t,x)||p&&(x=="length"||u&&(x=="offset"||x=="parent")||h&&(x=="buffer"||x=="byteLength"||x=="byteOffset")||Ye(x,v))||m.push(x);return m}function Ao(t){var e=t.length;return e?t[Ei(0,e-1)]:s}function fh(t,e){return cs(Jt(t),Sn(e,0,t.length))}function gh(t){return cs(Jt(t))}function _i(t,e,r){(r!==s&&!Ee(t[e],r)||r===s&&!(e in t))&&$e(t,e,r)}function dr(t,e,r){var a=t[e];lt.call(t,e)&&Ee(a,r)&&(r!==s||e in t)||$e(t,e,r)}function Jr(t,e){for(var r=t.length;r--;)if(Ee(t[r][0],e))return r;return-1}function ph(t,e,r,a){return an(t,function(u,h,p){e(a,u,r(u),p)}),a}function Fo(t,e){return t&&Ue(e,qt(e),t)}function $e(t,e,r){e=="__proto__"&&Wr?Wr(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}function mi(t,e){for(var r=-1,a=e.length,u=O(a),h=t==null;++r<a;)u[r]=h?s:Ki(t,e[r]);return u}function Sn(t,e,r){return t==t&&(r!==s&&(t=t<=r?t:r),e!==s&&(t=t>=e?t:e)),t}function ve(t,e,r,a,u,h){var p,m=1&e,v=2&e,x=4&e;if(r&&(p=u?r(t,a,u,h):r(t)),p!==s)return p;if(!St(t))return t;var C=K(t);if(C){if(p=function(w){var M=w.length,B=new w.constructor(M);return M&&typeof w[0]=="string"&&lt.call(w,"index")&&(B.index=w.index,B.input=w.input),B}(t),!m)return Jt(t,p)}else{var T=jt(t),P=T==ze||T==ne;if(ln(t))return tu(t,m);if(T==Rt||T==N||P&&!u){if(p=v||P?{}:yu(t),!m)return v?function(w,M){return Ue(w,mu(w),M)}(t,function(w,M){return w&&Ue(M,Xt(M),w)}(p,t)):function(w,M){return Ue(w,Hi(w),M)}(t,Fo(p,t))}else{if(!ht[T])return u?t:{};p=function(w,M,B){var R,Y=w.constructor;switch(M){case ir:return Oi(w);case vt:case z:return new Y(+w);case qn:return function(V,at){var H=at?Oi(V.buffer):V.buffer;return new V.constructor(H,V.byteOffset,V.byteLength)}(w,B);case Hs:case js:case zs:case Gs:case Ws:case Bs:case $s:case Vs:case Zs:return eu(w,B);case bt:return new Y;case Ce:case rr:return new Y(w);case ke:return function(V){var at=new V.constructor(V.source,Pa.exec(V));return at.lastIndex=V.lastIndex,at}(w);case xe:return new Y;case Ar:return R=w,hr?gt(hr.call(R)):{}}}(t,T,m)}}h||(h=new Ie);var L=h.get(t);if(L)return L;h.set(t,p),$u(t)?t.forEach(function(w){p.add(ve(w,e,r,w,t,h))}):Wu(t)&&t.forEach(function(w,M){p.set(M,ve(w,e,r,M,t,h))});var j=C?s:(x?v?Ni:Pi:v?Xt:qt)(t);return pe(j||t,function(w,M){j&&(w=t[M=w]),dr(p,M,ve(w,e,r,M,t,h))}),p}function Ro(t,e,r){var a=r.length;if(t==null)return!a;for(t=gt(t);a--;){var u=r[a],h=e[u],p=t[u];if(p===s&&!(u in t)||!h(p))return!1}return!0}function qo(t,e,r){if(typeof t!="function")throw new _e(n);return yr(function(){t.apply(s,r)},e)}function fr(t,e,r,a){var u=-1,h=Or,p=!0,m=t.length,v=[],x=e.length;if(!m)return v;r&&(e=yt(e,se(r))),a?(h=ri,p=!1):e.length>=200&&(h=ar,p=!1,e=new bn(e));t:for(;++u<m;){var C=t[u],T=r==null?C:r(C);if(C=a||C!==0?C:0,p&&T==T){for(var P=x;P--;)if(e[P]===T)continue t;v.push(C)}else h(e,T,a)||v.push(C)}return v}c.templateSettings={escape:Qc,evaluate:tl,interpolate:ka,variable:"",imports:{_:c}},c.prototype=Yr.prototype,c.prototype.constructor=c,me.prototype=jn(Yr.prototype),me.prototype.constructor=me,nt.prototype=jn(Yr.prototype),nt.prototype.constructor=nt,yn.prototype.clear=function(){this.__data__=lr?lr(null):{},this.size=0},yn.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},yn.prototype.get=function(t){var e=this.__data__;if(lr){var r=e[t];return r===i?s:r}return lt.call(e,t)?e[t]:s},yn.prototype.has=function(t){var e=this.__data__;return lr?e[t]!==s:lt.call(e,t)},yn.prototype.set=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=lr&&e===s?i:e,this},We.prototype.clear=function(){this.__data__=[],this.size=0},We.prototype.delete=function(t){var e=this.__data__,r=Jr(e,t);return!(r<0||(r==e.length-1?e.pop():Gr.call(e,r,1),--this.size,0))},We.prototype.get=function(t){var e=this.__data__,r=Jr(e,t);return r<0?s:e[r][1]},We.prototype.has=function(t){return Jr(this.__data__,t)>-1},We.prototype.set=function(t,e){var r=this.__data__,a=Jr(r,t);return a<0?(++this.size,r.push([t,e])):r[a][1]=e,this},Be.prototype.clear=function(){this.size=0,this.__data__={hash:new yn,map:new(ur||We),string:new yn}},Be.prototype.delete=function(t){var e=us(this,t).delete(t);return this.size-=e?1:0,e},Be.prototype.get=function(t){return us(this,t).get(t)},Be.prototype.has=function(t){return us(this,t).has(t)},Be.prototype.set=function(t,e){var r=us(this,t),a=r.size;return r.set(t,e),this.size+=r.size==a?0:1,this},bn.prototype.add=bn.prototype.push=function(t){return this.__data__.set(t,i),this},bn.prototype.has=function(t){return this.__data__.has(t)},Ie.prototype.clear=function(){this.__data__=new We,this.size=0},Ie.prototype.delete=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r},Ie.prototype.get=function(t){return this.__data__.get(t)},Ie.prototype.has=function(t){return this.__data__.has(t)},Ie.prototype.set=function(t,e){var r=this.__data__;if(r instanceof We){var a=r.__data__;if(!ur||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new Be(a)}return r.set(t,e),this.size=r.size,this};var an=iu(Ne),Oo=iu(yi,!0);function _h(t,e){var r=!0;return an(t,function(a,u,h){return r=!!e(a,u,h)}),r}function Kr(t,e,r){for(var a=-1,u=t.length;++a<u;){var h=t[a],p=e(h);if(p!=null&&(m===s?p==p&&!ae(p):r(p,m)))var m=p,v=h}return v}function Do(t,e){var r=[];return an(t,function(a,u,h){e(a,u,h)&&r.push(a)}),r}function Ut(t,e,r,a,u){var h=-1,p=t.length;for(r||(r=Mh),u||(u=[]);++h<p;){var m=t[h];e>0&&r(m)?e>1?Ut(m,e-1,r,a,u):nn(u,m):a||(u[u.length]=m)}return u}var vi=au(),ko=au(!0);function Ne(t,e){return t&&vi(t,e,qt)}function yi(t,e){return t&&ko(t,e,qt)}function Xr(t,e){return en(e,function(r){return Je(t[r])})}function Cn(t,e){for(var r=0,a=(e=un(e,t)).length;t!=null&&r<a;)t=t[Le(e[r++])];return r&&r==a?t:s}function Po(t,e,r){var a=e(t);return K(t)?a:nn(a,r(t))}function Bt(t){return t==null?t===s?"[object Undefined]":"[object Null]":vn&&vn in gt(t)?function(e){var r=lt.call(e,vn),a=e[vn];try{e[vn]=s;var u=!0}catch{}var h=Lr.call(e);return u&&(r?e[vn]=a:delete e[vn]),h}(t):function(e){return Lr.call(e)}(t)}function bi(t,e){return t>e}function mh(t,e){return t!=null&&lt.call(t,e)}function vh(t,e){return t!=null&&e in gt(t)}function Si(t,e,r){for(var a=r?ri:Or,u=t[0].length,h=t.length,p=h,m=O(h),v=1/0,x=[];p--;){var C=t[p];p&&e&&(C=yt(C,se(e))),v=Ht(C.length,v),m[p]=!r&&(e||u>=120&&C.length>=120)?new bn(p&&C):s}C=t[0];var T=-1,P=m[0];t:for(;++T<u&&x.length<v;){var L=C[T],j=e?e(L):L;if(L=r||L!==0?L:0,!(P?ar(P,j):a(x,j,r))){for(p=h;--p;){var w=m[p];if(!(w?ar(w,j):a(t[p],j,r)))continue t}P&&P.push(j),x.push(L)}}return x}function gr(t,e,r){var a=(t=xu(t,e=un(e,t)))==null?t:t[Le(be(e))];return a==null?s:re(a,t,r)}function No(t){return Ct(t)&&Bt(t)==N}function pr(t,e,r,a,u){return t===e||(t==null||e==null||!Ct(t)&&!Ct(e)?t!=t&&e!=e:function(h,p,m,v,x,C){var T=K(h),P=K(p),L=T?rt:jt(h),j=P?rt:jt(p),w=(L=L==N?Rt:L)==Rt,M=(j=j==N?Rt:j)==Rt,B=L==j;if(B&&ln(h)){if(!ln(p))return!1;T=!0,w=!1}if(B&&!w)return C||(C=new Ie),T||Bn(h)?_u(h,p,m,v,x,C):function(H,$,At,It,Vt,pt,zt){switch(At){case qn:if(H.byteLength!=$.byteLength||H.byteOffset!=$.byteOffset)return!1;H=H.buffer,$=$.buffer;case ir:return!(H.byteLength!=$.byteLength||!pt(new jr(H),new jr($)));case vt:case z:case Ce:return Ee(+H,+$);case dt:return H.name==$.name&&H.message==$.message;case ke:case rr:return H==$+"";case bt:var He=hi;case xe:var hn=1&It;if(He||(He=kr),H.size!=$.size&&!hn)return!1;var vs=zt.get(H);if(vs)return vs==$;It|=2,zt.set(H,$);var oa=_u(He(H),He($),It,Vt,pt,zt);return zt.delete(H),oa;case Ar:if(hr)return hr.call(H)==hr.call($)}return!1}(h,p,L,m,v,x,C);if(!(1&m)){var R=w&&lt.call(h,"__wrapped__"),Y=M&&lt.call(p,"__wrapped__");if(R||Y){var V=R?h.value():h,at=Y?p.value():p;return C||(C=new Ie),x(V,at,m,v,C)}}return!!B&&(C||(C=new Ie),function(H,$,At,It,Vt,pt){var zt=1&At,He=Pi(H),hn=He.length,vs=Pi($),oa=vs.length;if(hn!=oa&&!zt)return!1;for(var ys=hn;ys--;){var En=He[ys];if(!(zt?En in $:lt.call($,En)))return!1}var ac=pt.get(H),oc=pt.get($);if(ac&&oc)return ac==$&&oc==H;var bs=!0;pt.set(H,$),pt.set($,H);for(var ua=zt;++ys<hn;){var Ss=H[En=He[ys]],Cs=$[En];if(It)var uc=zt?It(Cs,Ss,En,$,H,pt):It(Ss,Cs,En,H,$,pt);if(!(uc===s?Ss===Cs||Vt(Ss,Cs,At,It,pt):uc)){bs=!1;break}ua||(ua=En=="constructor")}if(bs&&!ua){var xs=H.constructor,ws=$.constructor;xs==ws||!("constructor"in H)||!("constructor"in $)||typeof xs=="function"&&xs instanceof xs&&typeof ws=="function"&&ws instanceof ws||(bs=!1)}return pt.delete(H),pt.delete($),bs}(h,p,m,v,x,C))}(t,e,r,a,pr,u))}function Ci(t,e,r,a){var u=r.length,h=u,p=!a;if(t==null)return!h;for(t=gt(t);u--;){var m=r[u];if(p&&m[2]?m[1]!==t[m[0]]:!(m[0]in t))return!1}for(;++u<h;){var v=(m=r[u])[0],x=t[v],C=m[1];if(p&&m[2]){if(x===s&&!(v in t))return!1}else{var T=new Ie;if(a)var P=a(x,C,v,t,e,T);if(!(P===s?pr(C,x,3,a,T):P))return!1}}return!0}function Uo(t){return!(!St(t)||(e=t,bo&&bo in e))&&(Je(t)?Xl:pl).test(wn(t));var e}function Lo(t){return typeof t=="function"?t:t==null?Qt:typeof t=="object"?K(t)?zo(t[0],t[1]):jo(t):ic(t)}function xi(t){if(!vr(t))return sh(t);var e=[];for(var r in gt(t))lt.call(t,r)&&r!="constructor"&&e.push(r);return e}function yh(t){if(!St(t))return function(u){var h=[];if(u!=null)for(var p in gt(u))h.push(p);return h}(t);var e=vr(t),r=[];for(var a in t)(a!="constructor"||!e&&lt.call(t,a))&&r.push(a);return r}function wi(t,e){return t<e}function Ho(t,e){var r=-1,a=Kt(t)?O(t.length):[];return an(t,function(u,h,p){a[++r]=e(u,h,p)}),a}function jo(t){var e=Li(t);return e.length==1&&e[0][2]?Su(e[0][0],e[0][1]):function(r){return r===t||Ci(r,t,e)}}function zo(t,e){return ji(t)&&bu(e)?Su(Le(t),e):function(r){var a=Ki(r,t);return a===s&&a===e?Xi(r,t):pr(e,a,3)}}function Qr(t,e,r,a,u){t!==e&&vi(e,function(h,p){if(u||(u=new Ie),St(h))(function(v,x,C,T,P,L,j){var w=Gi(v,C),M=Gi(x,C),B=j.get(M);if(B)_i(v,C,B);else{var R=L?L(w,M,C+"",v,x,j):s,Y=R===s;if(Y){var V=K(M),at=!V&&ln(M),H=!V&&!at&&Bn(M);R=M,V||at||H?K(w)?R=w:wt(w)?R=Jt(w):at?(Y=!1,R=tu(M,!0)):H?(Y=!1,R=eu(M,!0)):R=[]:br(M)||In(M)?(R=w,In(w)?R=Yu(w):St(w)&&!Je(w)||(R=yu(M))):Y=!1}Y&&(j.set(M,R),P(R,M,T,L,j),j.delete(M)),_i(v,C,R)}})(t,e,p,r,Qr,a,u);else{var m=a?a(Gi(t,p),h,p+"",t,e,u):s;m===s&&(m=h),_i(t,p,m)}},Xt)}function Go(t,e){var r=t.length;if(r)return Ye(e+=e<0?r:0,r)?t[e]:s}function Wo(t,e,r){e=e.length?yt(e,function(h){return K(h)?function(p){return Cn(p,h.length===1?h[0]:h)}:h}):[Qt];var a=-1;e=yt(e,se(G()));var u=Ho(t,function(h,p,m){var v=yt(e,function(x){return x(h)});return{criteria:v,index:++a,value:h}});return function(h,p){var m=h.length;for(h.sort(p);m--;)h[m]=h[m].value;return h}(u,function(h,p){return function(m,v,x){for(var C=-1,T=m.criteria,P=v.criteria,L=T.length,j=x.length;++C<L;){var w=nu(T[C],P[C]);if(w)return C>=j?w:w*(x[C]=="desc"?-1:1)}return m.index-v.index}(h,p,r)})}function Bo(t,e,r){for(var a=-1,u=e.length,h={};++a<u;){var p=e[a],m=Cn(t,p);r(m,p)&&_r(h,un(p,t),m)}return h}function Ii(t,e,r,a){var u=a?jl:Dn,h=-1,p=e.length,m=t;for(t===e&&(e=Jt(e)),r&&(m=yt(t,se(r)));++h<p;)for(var v=0,x=e[h],C=r?r(x):x;(v=u(m,C,v,a))>-1;)m!==t&&Gr.call(m,v,1),Gr.call(t,v,1);return t}function $o(t,e){for(var r=t?e.length:0,a=r-1;r--;){var u=e[r];if(r==a||u!==h){var h=u;Ye(u)?Gr.call(t,u,1):Ai(t,u)}}return t}function Ei(t,e){return t+$r(Eo()*(e-t+1))}function Ti(t,e){var r="";if(!t||e<1||e>q)return r;do e%2&&(r+=t),(e=$r(e/2))&&(t+=t);while(e);return r}function Q(t,e){return Wi(Cu(t,e,Qt),t+"")}function bh(t){return Ao($n(t))}function Sh(t,e){var r=$n(t);return cs(r,Sn(e,0,r.length))}function _r(t,e,r,a){if(!St(t))return t;for(var u=-1,h=(e=un(e,t)).length,p=h-1,m=t;m!=null&&++u<h;){var v=Le(e[u]),x=r;if(v==="__proto__"||v==="constructor"||v==="prototype")return t;if(u!=p){var C=m[v];(x=a?a(C,v,m):s)===s&&(x=St(C)?C:Ye(e[u+1])?[]:{})}dr(m,v,x),m=m[v]}return t}var Vo=Vr?function(t,e){return Vr.set(t,e),t}:Qt,Ch=Wr?function(t,e){return Wr(t,"toString",{configurable:!0,enumerable:!1,value:ta(e),writable:!0})}:Qt;function xh(t){return cs($n(t))}function ye(t,e,r){var a=-1,u=t.length;e<0&&(e=-e>u?0:u+e),(r=r>u?u:r)<0&&(r+=u),u=e>r?0:r-e>>>0,e>>>=0;for(var h=O(u);++a<u;)h[a]=t[a+e];return h}function wh(t,e){var r;return an(t,function(a,u,h){return!(r=e(a,u,h))}),!!r}function ts(t,e,r){var a=0,u=t==null?a:t.length;if(typeof e=="number"&&e==e&&u<=2147483647){for(;a<u;){var h=a+u>>>1,p=t[h];p!==null&&!ae(p)&&(r?p<=e:p<e)?a=h+1:u=h}return u}return Mi(t,e,Qt,r)}function Mi(t,e,r,a){var u=0,h=t==null?0:t.length;if(h===0)return 0;for(var p=(e=r(e))!=e,m=e===null,v=ae(e),x=e===s;u<h;){var C=$r((u+h)/2),T=r(t[C]),P=T!==s,L=T===null,j=T==T,w=ae(T);if(p)var M=a||j;else M=x?j&&(a||P):m?j&&P&&(a||!L):v?j&&P&&!L&&(a||!w):!L&&!w&&(a?T<=e:T<e);M?u=C+1:h=C}return Ht(h,4294967294)}function Zo(t,e){for(var r=-1,a=t.length,u=0,h=[];++r<a;){var p=t[r],m=e?e(p):p;if(!r||!Ee(m,v)){var v=m;h[u++]=p===0?0:p}}return h}function Yo(t){return typeof t=="number"?t:ae(t)?D:+t}function ie(t){if(typeof t=="string")return t;if(K(t))return yt(t,ie)+"";if(ae(t))return To?To.call(t):"";var e=t+"";return e=="0"&&1/t==-1/0?"-0":e}function on(t,e,r){var a=-1,u=Or,h=t.length,p=!0,m=[],v=m;if(r)p=!1,u=ri;else if(h>=200){var x=e?null:Eh(t);if(x)return kr(x);p=!1,u=ar,v=new bn}else v=e?[]:m;t:for(;++a<h;){var C=t[a],T=e?e(C):C;if(C=r||C!==0?C:0,p&&T==T){for(var P=v.length;P--;)if(v[P]===T)continue t;e&&v.push(T),m.push(C)}else u(v,T,r)||(v!==m&&v.push(T),m.push(C))}return m}function Ai(t,e){return(t=xu(t,e=un(e,t)))==null||delete t[Le(be(e))]}function Jo(t,e,r,a){return _r(t,e,r(Cn(t,e)),a)}function es(t,e,r,a){for(var u=t.length,h=a?u:-1;(a?h--:++h<u)&&e(t[h],h,t););return r?ye(t,a?0:h,a?h+1:u):ye(t,a?h+1:0,a?u:h)}function Ko(t,e){var r=t;return r instanceof nt&&(r=r.value()),si(e,function(a,u){return u.func.apply(u.thisArg,nn([a],u.args))},r)}function Fi(t,e,r){var a=t.length;if(a<2)return a?on(t[0]):[];for(var u=-1,h=O(a);++u<a;)for(var p=t[u],m=-1;++m<a;)m!=u&&(h[u]=fr(h[u]||p,t[m],e,r));return on(Ut(h,1),e,r)}function Xo(t,e,r){for(var a=-1,u=t.length,h=e.length,p={};++a<u;){var m=a<h?e[a]:s;r(p,t[a],m)}return p}function Ri(t){return wt(t)?t:[]}function qi(t){return typeof t=="function"?t:Qt}function un(t,e){return K(t)?t:ji(t,e)?[t]:Tu(ot(t))}var Ih=Q;function cn(t,e,r){var a=t.length;return r=r===s?a:r,!e&&r>=a?t:ye(t,e,r)}var Qo=Ql||function(t){return Nt.clearTimeout(t)};function tu(t,e){if(e)return t.slice();var r=t.length,a=So?So(r):new t.constructor(r);return t.copy(a),a}function Oi(t){var e=new t.constructor(t.byteLength);return new jr(e).set(new jr(t)),e}function eu(t,e){var r=e?Oi(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.length)}function nu(t,e){if(t!==e){var r=t!==s,a=t===null,u=t==t,h=ae(t),p=e!==s,m=e===null,v=e==e,x=ae(e);if(!m&&!x&&!h&&t>e||h&&p&&v&&!m&&!x||a&&p&&v||!r&&v||!u)return 1;if(!a&&!h&&!x&&t<e||x&&r&&u&&!a&&!h||m&&r&&u||!p&&u||!v)return-1}return 0}function ru(t,e,r,a){for(var u=-1,h=t.length,p=r.length,m=-1,v=e.length,x=Mt(h-p,0),C=O(v+x),T=!a;++m<v;)C[m]=e[m];for(;++u<p;)(T||u<h)&&(C[r[u]]=t[u]);for(;x--;)C[m++]=t[u++];return C}function su(t,e,r,a){for(var u=-1,h=t.length,p=-1,m=r.length,v=-1,x=e.length,C=Mt(h-m,0),T=O(C+x),P=!a;++u<C;)T[u]=t[u];for(var L=u;++v<x;)T[L+v]=e[v];for(;++p<m;)(P||u<h)&&(T[L+r[p]]=t[u++]);return T}function Jt(t,e){var r=-1,a=t.length;for(e||(e=O(a));++r<a;)e[r]=t[r];return e}function Ue(t,e,r,a){var u=!r;r||(r={});for(var h=-1,p=e.length;++h<p;){var m=e[h],v=a?a(r[m],t[m],m,r,t):s;v===s&&(v=t[m]),u?$e(r,m,v):dr(r,m,v)}return r}function ns(t,e){return function(r,a){var u=K(r)?Nl:ph,h=e?e():{};return u(r,t,G(a,2),h)}}function zn(t){return Q(function(e,r){var a=-1,u=r.length,h=u>1?r[u-1]:s,p=u>2?r[2]:s;for(h=t.length>3&&typeof h=="function"?(u--,h):s,p&&$t(r[0],r[1],p)&&(h=u<3?s:h,u=1),e=gt(e);++a<u;){var m=r[a];m&&t(e,m,a,h)}return e})}function iu(t,e){return function(r,a){if(r==null)return r;if(!Kt(r))return t(r,a);for(var u=r.length,h=e?u:-1,p=gt(r);(e?h--:++h<u)&&a(p[h],h,p)!==!1;);return r}}function au(t){return function(e,r,a){for(var u=-1,h=gt(e),p=a(e),m=p.length;m--;){var v=p[t?m:++u];if(r(h[v],v,h)===!1)break}return e}}function ou(t){return function(e){var r=kn(e=ot(e))?we(e):s,a=r?r[0]:e.charAt(0),u=r?cn(r,1).join(""):e.slice(1);return a[t]()+u}}function Gn(t){return function(e){return si(rc(nc(e).replace(El,"")),t,"")}}function mr(t){return function(){var e=arguments;switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3]);case 5:return new t(e[0],e[1],e[2],e[3],e[4]);case 6:return new t(e[0],e[1],e[2],e[3],e[4],e[5]);case 7:return new t(e[0],e[1],e[2],e[3],e[4],e[5],e[6])}var r=jn(t.prototype),a=t.apply(r,e);return St(a)?a:r}}function uu(t){return function(e,r,a){var u=gt(e);if(!Kt(e)){var h=G(r,3);e=qt(e),r=function(m){return h(u[m],m,u)}}var p=t(e,r,a);return p>-1?u[h?e[p]:p]:s}}function cu(t){return Ze(function(e){var r=e.length,a=r,u=me.prototype.thru;for(t&&e.reverse();a--;){var h=e[a];if(typeof h!="function")throw new _e(n);if(u&&!p&&os(h)=="wrapper")var p=new me([],!0)}for(a=p?a:r;++a<r;){var m=os(h=e[a]),v=m=="wrapper"?Ui(h):s;p=v&&zi(v[0])&&v[1]==424&&!v[4].length&&v[9]==1?p[os(v[0])].apply(p,v[3]):h.length==1&&zi(h)?p[m]():p.thru(h)}return function(){var x=arguments,C=x[0];if(p&&x.length==1&&K(C))return p.plant(C).value();for(var T=0,P=r?e[T].apply(this,x):C;++T<r;)P=e[T].call(this,P);return P}})}function rs(t,e,r,a,u,h,p,m,v,x){var C=e&b,T=1&e,P=2&e,L=24&e,j=512&e,w=P?s:mr(t);return function M(){for(var B=arguments.length,R=O(B),Y=B;Y--;)R[Y]=arguments[Y];if(L)var V=Wn(M),at=function(It,Vt){for(var pt=It.length,zt=0;pt--;)It[pt]===Vt&&++zt;return zt}(R,V);if(a&&(R=ru(R,a,u,L)),h&&(R=su(R,h,p,L)),B-=at,L&&B<x){var H=rn(R,V);return du(t,e,rs,M.placeholder,r,R,H,m,v,x-B)}var $=T?r:this,At=P?$[t]:t;return B=R.length,m?R=function(It,Vt){for(var pt=It.length,zt=Ht(Vt.length,pt),He=Jt(It);zt--;){var hn=Vt[zt];It[zt]=Ye(hn,pt)?He[hn]:s}return It}(R,m):j&&B>1&&R.reverse(),C&&v<B&&(R.length=v),this&&this!==Nt&&this instanceof M&&(At=w||mr(At)),At.apply($,R)}}function lu(t,e){return function(r,a){return function(u,h,p,m){return Ne(u,function(v,x,C){h(m,p(v),x,C)}),m}(r,t,e(a),{})}}function ss(t,e){return function(r,a){var u;if(r===s&&a===s)return e;if(r!==s&&(u=r),a!==s){if(u===s)return a;typeof r=="string"||typeof a=="string"?(r=ie(r),a=ie(a)):(r=Yo(r),a=Yo(a)),u=t(r,a)}return u}}function Di(t){return Ze(function(e){return e=yt(e,se(G())),Q(function(r){var a=this;return t(e,function(u){return re(u,a,r)})})})}function is(t,e){var r=(e=e===s?" ":ie(e)).length;if(r<2)return r?Ti(e,t):e;var a=Ti(e,Br(t/Pn(e)));return kn(e)?cn(we(a),0,t).join(""):a.slice(0,t)}function hu(t){return function(e,r,a){return a&&typeof a!="number"&&$t(e,r,a)&&(r=a=s),e=Ke(e),r===s?(r=e,e=0):r=Ke(r),function(u,h,p,m){for(var v=-1,x=Mt(Br((h-u)/(p||1)),0),C=O(x);x--;)C[m?x:++v]=u,u+=p;return C}(e,r,a=a===s?e<r?1:-1:Ke(a),t)}}function as(t){return function(e,r){return typeof e=="string"&&typeof r=="string"||(e=Se(e),r=Se(r)),t(e,r)}}function du(t,e,r,a,u,h,p,m,v,x){var C=8&e;e|=C?f:_,4&(e&=~(C?_:f))||(e&=-4);var T=[t,e,u,C?h:s,C?p:s,C?s:h,C?s:p,m,v,x],P=r.apply(s,T);return zi(t)&&wu(P,T),P.placeholder=a,Iu(P,t,e)}function ki(t){var e=Pe[t];return function(r,a){if(r=Se(r),(a=a==null?0:Ht(X(a),292))&&Io(r)){var u=(ot(r)+"e").split("e");return+((u=(ot(e(u[0]+"e"+(+u[1]+a)))+"e").split("e"))[0]+"e"+(+u[1]-a))}return e(r)}}var Eh=Ln&&1/kr(new Ln([,-0]))[1]==A?function(t){return new Ln(t)}:ra;function fu(t){return function(e){var r=jt(e);return r==bt?hi(e):r==xe?Bl(e):function(a,u){return yt(u,function(h){return[h,a[h]]})}(e,t(e))}}function Ve(t,e,r,a,u,h,p,m){var v=2&e;if(!v&&typeof t!="function")throw new _e(n);var x=a?a.length:0;if(x||(e&=-97,a=u=s),p=p===s?p:Mt(X(p),0),m=m===s?m:X(m),x-=u?u.length:0,e&_){var C=a,T=u;a=u=s}var P=v?s:Ui(t),L=[t,e,r,a,u,C,T,h,p,m];if(P&&function(w,M){var B=w[1],R=M[1],Y=B|R,V=Y<131,at=R==b&&B==8||R==b&&B==S&&w[7].length<=M[8]||R==384&&M[7].length<=M[8]&&B==8;if(!V&&!at)return w;1&R&&(w[2]=M[2],Y|=1&B?0:4);var H=M[3];if(H){var $=w[3];w[3]=$?ru($,H,M[4]):H,w[4]=$?rn(w[3],o):M[4]}(H=M[5])&&($=w[5],w[5]=$?su($,H,M[6]):H,w[6]=$?rn(w[5],o):M[6]),(H=M[7])&&(w[7]=H),R&b&&(w[8]=w[8]==null?M[8]:Ht(w[8],M[8])),w[9]==null&&(w[9]=M[9]),w[0]=M[0],w[1]=Y}(L,P),t=L[0],e=L[1],r=L[2],a=L[3],u=L[4],!(m=L[9]=L[9]===s?v?0:t.length:Mt(L[9]-x,0))&&24&e&&(e&=-25),e&&e!=1)j=e==8||e==l?function(w,M,B){var R=mr(w);return function Y(){for(var V=arguments.length,at=O(V),H=V,$=Wn(Y);H--;)at[H]=arguments[H];var At=V<3&&at[0]!==$&&at[V-1]!==$?[]:rn(at,$);return(V-=At.length)<B?du(w,M,rs,Y.placeholder,s,at,At,s,s,B-V):re(this&&this!==Nt&&this instanceof Y?R:w,this,at)}}(t,e,m):e!=f&&e!=33||u.length?rs.apply(s,L):function(w,M,B,R){var Y=1&M,V=mr(w);return function at(){for(var H=-1,$=arguments.length,At=-1,It=R.length,Vt=O(It+$),pt=this&&this!==Nt&&this instanceof at?V:w;++At<It;)Vt[At]=R[At];for(;$--;)Vt[At++]=arguments[++H];return re(pt,Y?B:this,Vt)}}(t,e,r,a);else var j=function(w,M,B){var R=1&M,Y=mr(w);return function V(){return(this&&this!==Nt&&this instanceof V?Y:w).apply(R?B:this,arguments)}}(t,e,r);return Iu((P?Vo:wu)(j,L),t,e)}function gu(t,e,r,a){return t===s||Ee(t,Un[r])&&!lt.call(a,r)?e:t}function pu(t,e,r,a,u,h){return St(t)&&St(e)&&(h.set(e,t),Qr(t,e,s,pu,h),h.delete(e)),t}function Th(t){return br(t)?s:t}function _u(t,e,r,a,u,h){var p=1&r,m=t.length,v=e.length;if(m!=v&&!(p&&v>m))return!1;var x=h.get(t),C=h.get(e);if(x&&C)return x==e&&C==t;var T=-1,P=!0,L=2&r?new bn:s;for(h.set(t,e),h.set(e,t);++T<m;){var j=t[T],w=e[T];if(a)var M=p?a(w,j,T,e,t,h):a(j,w,T,t,e,h);if(M!==s){if(M)continue;P=!1;break}if(L){if(!ii(e,function(B,R){if(!ar(L,R)&&(j===B||u(j,B,r,a,h)))return L.push(R)})){P=!1;break}}else if(j!==w&&!u(j,w,r,a,h)){P=!1;break}}return h.delete(t),h.delete(e),P}function Ze(t){return Wi(Cu(t,s,Ru),t+"")}function Pi(t){return Po(t,qt,Hi)}function Ni(t){return Po(t,Xt,mu)}var Ui=Vr?function(t){return Vr.get(t)}:ra;function os(t){for(var e=t.name+"",r=Hn[e],a=lt.call(Hn,e)?r.length:0;a--;){var u=r[a],h=u.func;if(h==null||h==t)return u.name}return e}function Wn(t){return(lt.call(c,"placeholder")?c:t).placeholder}function G(){var t=c.iteratee||ea;return t=t===ea?Lo:t,arguments.length?t(arguments[0],arguments[1]):t}function us(t,e){var r,a,u=t.__data__;return((a=typeof(r=e))=="string"||a=="number"||a=="symbol"||a=="boolean"?r!=="__proto__":r===null)?u[typeof e=="string"?"string":"hash"]:u.map}function Li(t){for(var e=qt(t),r=e.length;r--;){var a=e[r],u=t[a];e[r]=[a,u,bu(u)]}return e}function xn(t,e){var r=function(a,u){return a==null?s:a[u]}(t,e);return Uo(r)?r:s}var Hi=fi?function(t){return t==null?[]:(t=gt(t),en(fi(t),function(e){return xo.call(t,e)}))}:sa,mu=fi?function(t){for(var e=[];t;)nn(e,Hi(t)),t=zr(t);return e}:sa,jt=Bt;function vu(t,e,r){for(var a=-1,u=(e=un(e,t)).length,h=!1;++a<u;){var p=Le(e[a]);if(!(h=t!=null&&r(t,p)))break;t=t[p]}return h||++a!=u?h:!!(u=t==null?0:t.length)&&ps(u)&&Ye(p,u)&&(K(t)||In(t))}function yu(t){return typeof t.constructor!="function"||vr(t)?{}:jn(zr(t))}function Mh(t){return K(t)||In(t)||!!(wo&&t&&t[wo])}function Ye(t,e){var r=typeof t;return!!(e=e??q)&&(r=="number"||r!="symbol"&&ml.test(t))&&t>-1&&t%1==0&&t<e}function $t(t,e,r){if(!St(r))return!1;var a=typeof e;return!!(a=="number"?Kt(r)&&Ye(e,r.length):a=="string"&&e in r)&&Ee(r[e],t)}function ji(t,e){if(K(t))return!1;var r=typeof t;return!(r!="number"&&r!="symbol"&&r!="boolean"&&t!=null&&!ae(t))||nl.test(t)||!el.test(t)||e!=null&&t in gt(e)}function zi(t){var e=os(t),r=c[e];if(typeof r!="function"||!(e in nt.prototype))return!1;if(t===r)return!0;var a=Ui(r);return!!a&&t===a[0]}(gi&&jt(new gi(new ArrayBuffer(1)))!=qn||ur&&jt(new ur)!=bt||pi&&jt(pi.resolve())!=Ge||Ln&&jt(new Ln)!=xe||cr&&jt(new cr)!=sr)&&(jt=function(t){var e=Bt(t),r=e==Rt?t.constructor:s,a=r?wn(r):"";if(a)switch(a){case uh:return qn;case ch:return bt;case lh:return Ge;case hh:return xe;case dh:return sr}return e});var Ah=Nr?Je:ia;function vr(t){var e=t&&t.constructor;return t===(typeof e=="function"&&e.prototype||Un)}function bu(t){return t==t&&!St(t)}function Su(t,e){return function(r){return r!=null&&r[t]===e&&(e!==s||t in gt(r))}}function Cu(t,e,r){return e=Mt(e===s?t.length-1:e,0),function(){for(var a=arguments,u=-1,h=Mt(a.length-e,0),p=O(h);++u<h;)p[u]=a[e+u];u=-1;for(var m=O(e+1);++u<e;)m[u]=a[u];return m[e]=r(p),re(t,this,m)}}function xu(t,e){return e.length<2?t:Cn(t,ye(e,0,-1))}function Gi(t,e){if((e!=="constructor"||typeof t[e]!="function")&&e!="__proto__")return t[e]}var wu=Eu(Vo),yr=eh||function(t,e){return Nt.setTimeout(t,e)},Wi=Eu(Ch);function Iu(t,e,r){var a=e+"";return Wi(t,function(u,h){var p=h.length;if(!p)return u;var m=p-1;return h[m]=(p>1?"& ":"")+h[m],h=h.join(p>2?", ":" "),u.replace(al,`{
