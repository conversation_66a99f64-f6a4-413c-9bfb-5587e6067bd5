import asyncio
import json
from typing import List, Dict, Any
from dataclasses import dataclass
import aiohttp
import ast
import sys
from enum import Enum
import os
import tomli
from openai import AsyncOpenAI
from googlesearch import search

# 简化的工具定义
class ToolType(Enum):
    PYTHON_EXECUTE = "python_execute"
    GOOGLE_SEARCH = "google_search"
    TERMINATE = "terminate"

@dataclass
class ToolCall:
    tool: str
    args: Dict[str, Any]

class SimpleManus:
    def __init__(self):
        # 加载配置文件
        with open("config/config.toml", "rb") as f:
            self.config = tomli.load(f)
        
        # 初始化OpenAI客户端（使用deepseek配置）
        self.client = AsyncOpenAI(
            base_url=self.config["llm"]["base_url"],
            api_key=self.config["llm"]["api_key"]
        )
        
        self.system_prompt = """You are an AI assistant that can help users with various tasks.
You have access to the following tools:
1. python_execute: Execute Python code and return the result
2. google_search: Search Google for information
3. terminate: End the conversation

When you want to use a tool, respond with a JSON object in the format:
{
    "thought": "your thought process",
    "tool": "tool_name",
    "args": {
        "arg1": "value1",
        ...
    }
}

For python_execute, the args should contain a "code" key with the Python code to execute.
For google_search, the args should contain a "query" key with the search query.
For terminate, no args are needed.

Always format your response as a valid JSON object."""

    async def execute_tool(self, tool_call: ToolCall) -> str:
        if tool_call.tool == ToolType.PYTHON_EXECUTE.value:
            try:
                # 创建一个本地的命名空间来执行代码
                local_ns = {}
                exec(tool_call.args["code"], {}, local_ns)
                return str(local_ns.get("result", "Code executed successfully"))
            except Exception as e:
                return f"Error executing Python code: {str(e)}"
            
        elif tool_call.tool == ToolType.GOOGLE_SEARCH.value:
            try:
                # 使用googlesearch-python包执行实际的搜索
                results = []
                for url in search(tool_call.args["query"], num_results=5):
                    results.append(url)
                return "Search results:\n" + "\n".join(results)
            except Exception as e:
                return f"Error performing Google search: {str(e)}"
            
        elif tool_call.tool == ToolType.TERMINATE.value:
            return "Conversation ended."
        
        return "Unknown tool"

    def parse_llm_response(self, response: str) -> ToolCall:
        try:
            # 解析LLM的JSON响应
            parsed = json.loads(response)
            return ToolCall(
                tool=parsed["tool"],
                args=parsed["args"]
            )
        except Exception as e:
            return ToolCall(
                tool="python_execute",
                args={"code": f"result = 'Error parsing LLM response: {str(e)}'"}
            )

    async def get_llm_response(self, prompt: str) -> str:
        try:
            # 调用Deepseek API
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": prompt}
            ]
            
            response = await self.client.chat.completions.create(
                model=self.config["llm"]["model"],
                messages=messages,
                temperature=self.config["llm"]["temperature"],
                max_tokens=self.config["llm"]["max_tokens"]
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            # 如果API调用失败，返回一个基本的错误响应
            return json.dumps({
                "thought": "Error calling Deepseek API",
                "tool": "python_execute",
                "args": {
                    "code": f"result = 'Error calling Deepseek API: {str(e)}'"
                }
            })

    async def run(self, prompt: str):
        if prompt.lower() == "exit":
            return "Goodbye!"

        # 获取LLM响应
        llm_response = await self.get_llm_response(prompt)
        print(f"LLM Response: {llm_response}")

        # 解析LLM响应
        tool_call = self.parse_llm_response(llm_response)
        
        # 执行工具调用
        result = await self.execute_tool(tool_call)
        return result

async def main():
    # 检查配置文件是否存在
    if not os.path.exists("config/config.toml"):
        print("Error: config/config.toml file not found")
        sys.exit(1)
        
    agent = SimpleManus()
    while True:
        try:
            prompt = input("Enter your prompt (or 'exit' to quit): ")
            if prompt.lower() == "exit":
                print("Goodbye!")
                break
            
            print("Processing your request...")
            result = await agent.run(prompt)
            print(f"Result: {result}")
            
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break

if __name__ == "__main__":
    asyncio.run(main()) 